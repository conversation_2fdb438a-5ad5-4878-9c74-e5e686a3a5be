# User-DF: 用户数据处理和向量化系统

[![Python Version](https://img.shields.io/badge/python-3.8%2B-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Version](https://img.shields.io/badge/version-2.0.0-orange.svg)](pyproject.toml)

User-DF 是一个高性能的用户数据处理和向量化系统，专为大规模用户数据分析和机器学习应用而设计。系统采用微服务架构，支持 ORC 文件处理、MongoDB 存储、Milvus 向量数据库和 PCA 降维等功能。

## 🚀 核心特性

- **微服务架构**: 模块化设计，易于扩展和维护
- **大规模数据处理**: 支持 20 亿用户规模的数据处理
- **多数据库支持**: MongoDB、Milvus、Redis 等
- **向量化处理**: 支持 512D 到 256D 的 PCA 降维
- **批处理优化**: 高效的批量数据处理和存储
- **实时监控**: 完整的服务监控和日志系统
- **配置管理**: 灵活的 YAML 配置文件管理

## 📋 系统架构

```
User-DF/
├── services/                    # 微服务模块
│   ├── orc_mongodb_service/     # ORC文件处理和MongoDB存储服务
│   └── user_vector_service/     # 用户向量化处理服务
├── shared/                      # 共享模块
│   ├── core/                    # 核心功能（配置、日志、异常）
│   ├── database/                # 数据库连接模块
│   ├── models/                  # 数据模型
│   ├── queue/                   # 队列管理
│   └── utils/                   # 工具函数
├── configs/                     # 配置文件
├── models/                      # 预训练模型
├── logs/                        # 日志文件
└── tools/                       # 工具脚本
```

## 🛠️ 快速开始

### 环境要求

- Python 3.8+
- MongoDB 4.4+
- Milvus 2.5+
- Redis 6.0+

### 安装

```bash
# 克隆项目
git clone https://github.com/user-df/User-DF.git
cd User-DF

# 安装依赖
pip install -e .

# 或安装所有可选依赖
pip install -e .[all]
```

### 配置

1. 复制配置模板：
```bash
cp configs/orc_mongodb_service/development.yaml.template configs/orc_mongodb_service/development.yaml
cp configs/user_vector_service/development.yaml.template configs/user_vector_service/development.yaml
```

2. 修改配置文件中的数据库连接信息

### 启动服务

#### ORC MongoDB 服务
```bash
# 启动所有微服务
python3 services/orc_mongodb_service/start_services.py

# 检查服务状态
python3 services/orc_mongodb_service/start_services.py --status

# 停止服务
python3 services/orc_mongodb_service/start_services.py --stop
```

#### 用户向量服务
```bash
# 启动所有微服务
python3 services/user_vector_service/start_services.py

# 检查服务状态
python3 services/user_vector_service/start_services.py --status

# 停止服务
python3 services/user_vector_service/start_services.py --stop
```

## 📊 服务详情

### ORC MongoDB 服务
- **ORC处理微服务** (端口 8001): 处理 ORC 文件读取和数据解析
- **MongoDB写入微服务** (端口 8002): 负责用户数据的 MongoDB 存储

### 用户向量服务
- **MongoDB读取微服务** (端口 8003): 从 MongoDB 读取用户数据
- **向量计算微服务** (端口 8004): 执行 PCA 降维和向量计算
- **向量存储微服务** (端口 8005): 将用户向量存储到 Milvus

## 🔧 配置说明

系统使用 YAML 配置文件进行管理，主要配置项包括：

- **数据库配置**: MongoDB、Milvus、Redis 连接信息
- **处理参数**: 批处理大小、超时设置、重试次数
- **日志配置**: 日志级别、文件路径、轮转设置
- **监控配置**: 健康检查、性能监控参数

详细配置说明请参考 `docs/CONFIGURATION.md`

## 📈 性能特性

- **批处理优化**: 支持 1000 用户/批次的高效处理
- **内存管理**: 智能的内存使用和垃圾回收
- **并发处理**: 多进程和异步处理支持
- **数据压缩**: 优化的数据存储格式
- **缓存机制**: Redis 缓存提升查询性能

## 🔍 监控和日志

- **实时监控**: 服务健康状态和性能指标
- **结构化日志**: 统一的日志格式和级别管理
- **错误追踪**: 详细的错误信息和堆栈跟踪
- **性能分析**: 处理时间和资源使用统计

## 🧪 测试

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/unit/
pytest tests/integration/

# 生成覆盖率报告
pytest --cov=shared --cov=services --cov-report=html
```

## 📚 文档

- [架构设计](docs/ARCHITECTURE.md)
- [配置指南](docs/CONFIGURATION.md)
- [部署指南](docs/DEPLOYMENT.md)
- [API 文档](docs/API.md)
- [故障排除](docs/TROUBLESHOOTING.md)

## 🤝 贡献

欢迎贡献代码！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解详细信息。

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

- 问题报告: [GitHub Issues](https://github.com/user-df/User-DF/issues)
- 文档: [ReadTheDocs](https://user-df.readthedocs.io/)
- 邮件: <EMAIL>
