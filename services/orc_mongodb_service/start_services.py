#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ORC MongoDB服务启动脚本

启动ORC MongoDB服务的所有微服务：
- ORC处理微服务 (端口 8001)
- MongoDB写入微服务 (端口 8002)
"""

import os
import sys
import time
import argparse
import subprocess
import signal
import yaml
from typing import Dict, List, Optional
import requests

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from shared.core import ConfigManager, Logger


class ORCMongoDBServiceManager:
    """ORC MongoDB服务管理器"""

    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or "configs/orc_mongodb_service/development.yaml"
        self.logger = Logger.get_logger("ORCMongoDBServiceManager")

        # 从主配置文件加载服务配置
        self.service_configs = self._load_service_configs()

        # 服务配置
        self.services = {
            "orc_processor": {
                "name": "ORC处理微服务",
                "module": "services.orc_mongodb_service.orc_processor_service.main",
                "port": 8001,
                "process": None,
                "tmux_session": "orc-mongodb-processor",
                "config_file": self.service_configs.get("orc_processor_service", {}).get("config_file")
            },
            "mongodb_writer": {
                "name": "MongoDB写入微服务",
                "module": "services.orc_mongodb_service.mongodb_writer_service.main",
                "port": 8002,
                "process": None,
                "tmux_session": "orc-mongodb-writer",
                "config_file": self.service_configs.get("mongodb_writer_service", {}).get("config_file")
            }
        }

        # 进程管理
        self.processes = {}
        self.is_running = False

    def _load_service_configs(self) -> Dict[str, Dict[str, str]]:
        """从主配置文件加载服务配置文件路径"""
        try:
            if not os.path.exists(self.config_path):
                self.logger.warning(f"主配置文件不存在: {self.config_path}")
                return {}

            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f) or {}

            services_config = config.get("services", {})
            self.logger.info(f"从主配置文件加载了 {len(services_config)} 个服务配置")
            return services_config

        except Exception as e:
            self.logger.error(f"加载主配置文件失败: {e}")
            return {}

    def start_all_services(self, use_tmux: bool = True):
        """启动所有服务"""
        try:
            self.logger.info("=== 启动ORC MongoDB服务微服务架构 ===")

            # 检查端口占用
            self._check_ports()

            # 启动各个服务
            for service_key, service_config in self.services.items():
                if use_tmux:
                    self._start_service_with_tmux(service_key, service_config)
                else:
                    self._start_service_direct(service_key, service_config)

                # 等待服务启动
                time.sleep(2)

            # 等待所有服务就绪
            self._wait_for_services_ready()

            self.is_running = True
            self.logger.info("=== 所有ORC MongoDB服务微服务启动完成 ===")

            # 显示服务状态
            self._show_service_status()

        except Exception as e:
            self.logger.error(f"启动服务失败: {e}")
            self.stop_all_services()
            raise

    def _check_ports(self):
        """检查端口占用"""
        import socket

        for service_key, service_config in self.services.items():
            port = service_config["port"]

            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                result = sock.connect_ex(('localhost', port))
                if result == 0:
                    self.logger.warning(f"端口 {port} 已被占用 ({service_config['name']})")
                    # 尝试停止现有服务
                    self._kill_process_on_port(port)

    def _kill_process_on_port(self, port: int):
        """杀死占用指定端口的进程"""
        try:
            # 使用lsof查找占用端口的进程
            result = subprocess.run(
                ["lsof", "-ti", f":{port}"],
                capture_output=True,
                text=True
            )

            if result.returncode == 0 and result.stdout.strip():
                pids = result.stdout.strip().split('\n')
                for pid in pids:
                    if pid:
                        subprocess.run(["kill", "-9", pid])
                        self.logger.info(f"已杀死占用端口 {port} 的进程 {pid}")

        except Exception as e:
            self.logger.warning(f"无法杀死占用端口 {port} 的进程: {e}")

    def _start_service_with_tmux(self, service_key: str, service_config: Dict):
        """使用tmux启动服务"""
        try:
            session_name = service_config["tmux_session"]
            module_name = service_config["module"]
            port = service_config["port"]
            service_config_file = service_config.get("config_file")

            # 构建启动命令
            cmd_parts = [
                "python3", "-m", module_name,
                "--host", "0.0.0.0",
                "--port", str(port)
            ]

            # 使用服务特定的配置文件，如果没有则使用主配置文件
            config_file_to_use = service_config_file or self.config_path
            if config_file_to_use:
                cmd_parts.extend(["--config", config_file_to_use])

            cmd = " ".join(cmd_parts)

            # 检查tmux会话是否已存在
            check_session = subprocess.run(
                ["tmux", "has-session", "-t", session_name],
                capture_output=True
            )

            if check_session.returncode == 0:
                # 会话已存在，杀死它
                subprocess.run(["tmux", "kill-session", "-t", session_name])
                time.sleep(1)

            # 创建新的tmux会话
            subprocess.run([
                "tmux", "new-session", "-d", "-s", session_name, cmd
            ])

            self.logger.info(f"已在tmux会话 '{session_name}' 中启动 {service_config['name']} (配置: {config_file_to_use})")

        except Exception as e:
            self.logger.error(f"使用tmux启动服务失败 {service_key}: {e}")
            raise

    def _start_service_direct(self, service_key: str, service_config: Dict):
        """直接启动服务"""
        try:
            module_name = service_config["module"]
            port = service_config["port"]
            service_config_file = service_config.get("config_file")

            # 构建启动命令
            cmd = [
                sys.executable, "-m", module_name,
                "--host", "0.0.0.0",
                "--port", str(port)
            ]

            # 使用服务特定的配置文件，如果没有则使用主配置文件
            config_file_to_use = service_config_file or self.config_path
            if config_file_to_use:
                cmd.extend(["--config", config_file_to_use])

            # 启动进程
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            self.processes[service_key] = process
            self.logger.info(f"已启动 {service_config['name']} (PID: {process.pid}, 配置: {config_file_to_use})")

        except Exception as e:
            self.logger.error(f"直接启动服务失败 {service_key}: {e}")
            raise

    def _wait_for_services_ready(self, timeout: int = 60):
        """等待所有服务就绪"""
        self.logger.info("等待所有服务就绪...")

        start_time = time.time()

        while time.time() - start_time < timeout:
            all_ready = True

            for service_key, service_config in self.services.items():
                port = service_config["port"]

                try:
                    response = requests.get(f"http://localhost:{port}/health", timeout=2)
                    if response.status_code != 200:
                        all_ready = False
                        break
                except:
                    all_ready = False
                    break

            if all_ready:
                self.logger.info("所有服务已就绪")
                return

            time.sleep(2)

        raise Exception(f"等待服务就绪超时 ({timeout}秒)")

    def _show_service_status(self):
        """显示服务状态"""
        self.logger.info("\n=== 服务状态 ===")

        for service_key, service_config in self.services.items():
            name = service_config["name"]
            port = service_config["port"]

            try:
                response = requests.get(f"http://localhost:{port}/health", timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    status = "✅ 运行中"
                    uptime = data.get("uptime", 0)
                    version = data.get("version", "unknown")
                    self.logger.info(f"{name}: {status} (端口: {port}, 运行时间: {uptime:.1f}s, 版本: {version})")
                else:
                    self.logger.info(f"{name}: ❌ 异常 (端口: {port}, HTTP {response.status_code})")
            except Exception as e:
                self.logger.info(f"{name}: ❌ 无法连接 (端口: {port}, 错误: {e})")

        self.logger.info("\n=== 管理命令 ===")
        self.logger.info("查看服务日志: tmux attach -t <session-name>")
        self.logger.info("停止所有服务: python3 services/orc_mongodb_service/start_services.py --stop")
        self.logger.info("监控服务状态: python3 -m services.orc_mongodb_service.monitoring_service.monitor")

    def stop_all_services(self):
        """停止所有服务"""
        try:
            self.logger.info("=== 停止ORC MongoDB服务微服务 ===")

            # 停止tmux会话
            for service_key, service_config in self.services.items():
                session_name = service_config["tmux_session"]

                try:
                    # 检查会话是否存在
                    check_session = subprocess.run(
                        ["tmux", "has-session", "-t", session_name],
                        capture_output=True
                    )

                    if check_session.returncode == 0:
                        subprocess.run(["tmux", "kill-session", "-t", session_name])
                        self.logger.info(f"已停止tmux会话: {session_name}")

                except Exception as e:
                    self.logger.warning(f"停止tmux会话失败 {session_name}: {e}")

            # 停止直接启动的进程
            for service_key, process in self.processes.items():
                try:
                    if process and process.poll() is None:
                        process.terminate()
                        process.wait(timeout=5)
                        self.logger.info(f"已停止进程: {service_key}")
                except Exception as e:
                    self.logger.warning(f"停止进程失败 {service_key}: {e}")

            self.is_running = False
            self.logger.info("=== 所有ORC MongoDB服务微服务已停止 ===")

        except Exception as e:
            self.logger.error(f"停止服务失败: {e}")


    def check_status(self):
        """检查服务状态"""
        self.logger.info("=== ORC MongoDB服务状态检查 ===")

        for service_key, service_config in self.services.items():
            name = service_config["name"]
            port = service_config["port"]
            session_name = service_config["tmux_session"]

            # 检查tmux会话
            try:
                check_session = subprocess.run(
                    ["tmux", "has-session", "-t", session_name],
                    capture_output=True
                )

                tmux_status = "运行中" if check_session.returncode == 0 else "未运行"
            except:
                tmux_status = "未知"

            # 检查HTTP服务
            try:
                response = requests.get(f"http://localhost:{port}/health", timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    http_status = f"✅ 健康 (运行时间: {data.get('uptime', 0):.1f}s)"
                else:
                    http_status = f"❌ 异常 (HTTP {response.status_code})"
            except Exception as e:
                http_status = f"❌ 无法连接 ({str(e)[:50]})"

            self.logger.info(f"{name}:")
            self.logger.info(f"  端口: {port}")
            self.logger.info(f"  tmux会话: {session_name} ({tmux_status})")
            self.logger.info(f"  HTTP状态: {http_status}")
            self.logger.info("")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="ORC MongoDB服务管理器")
    parser.add_argument("--config", help="配置文件路径")
    parser.add_argument("--stop", action="store_true", help="停止所有服务")
    parser.add_argument("--status", action="store_true", help="检查服务状态")
    parser.add_argument("--no-tmux", action="store_true", help="不使用tmux启动服务")

    args = parser.parse_args()

    try:
        # 设置环境变量
        if args.config:
            os.environ['USER_DF_CONFIG_FILE'] = args.config

        manager = ORCMongoDBServiceManager(args.config)

        if args.stop:
            manager.stop_all_services()
        elif args.status:
            manager.check_status()
        else:
            # 设置信号处理
            def signal_handler(signum, frame):
                print("\n接收到中断信号，正在停止服务...")
                manager.stop_all_services()
                sys.exit(0)

            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)

            # 启动服务
            manager.start_all_services(use_tmux=not args.no_tmux)

            # 保持运行
            try:
                while manager.is_running:
                    time.sleep(1)
            except KeyboardInterrupt:
                manager.stop_all_services()

    except Exception as e:
        print(f"服务管理失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
