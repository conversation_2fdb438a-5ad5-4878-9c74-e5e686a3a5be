# 用户向量服务配置使用说明

## 概述

用户向量服务现在采用与 `orc_mongodb_service` 相同的配置管理方式，即：
- 总目录的配置文件仅用于调用子服务的配置文件地址
- 每个微服务使用独立的配置文件
- 支持开发环境和生产环境的配置分离

## 配置文件结构

```
configs/user_vector_service/
├── development.yaml                    # 开发环境主配置文件
├── production.yaml                     # 生产环境主配置文件
├── mongodb_reader_service/
│   ├── development.yaml               # MongoDB读取服务开发环境配置
│   └── production.yaml                # MongoDB读取服务生产环境配置
├── vector_processor_service/
│   ├── development.yaml               # 向量处理服务开发环境配置
│   └── production.yaml                # 向量处理服务生产环境配置
├── vector_writer_service/
│   ├── development.yaml               # 向量写入服务开发环境配置
│   └── production.yaml                # 向量写入服务生产环境配置
└── user_vector_monitoring_service/
    ├── development.yaml               # 监控服务开发环境配置
    └── production.yaml                # 监控服务生产环境配置
```

## 主配置文件格式

主配置文件（`development.yaml` 和 `production.yaml`）的格式如下：

```yaml
# 用户向量服务 - 开发环境全局配置文件
# 版本: 2.0.0
# 包含各个微服务的配置文件路径

# ==================== 全局配置 ====================
project:
  name: "User-DF"
  version: "2.0.0"
  environment: "development"

# ==================== 微服务配置文件路径 ====================
services:
  # MongoDB读取服务配置文件路径
  mongodb_reader_service:
    config_file: "configs/user_vector_service/mongodb_reader_service/development.yaml"

  # 向量处理服务配置文件路径
  vector_processor_service:
    config_file: "configs/user_vector_service/vector_processor_service/development.yaml"

  # 向量写入服务配置文件路径
  vector_writer_service:
    config_file: "configs/user_vector_service/vector_writer_service/development.yaml"

  # 监控服务配置文件路径（如果需要）
  user_vector_monitoring_service:
    config_file: "configs/user_vector_service/user_vector_monitoring_service/development.yaml"
```

## 使用方法

### 1. 启动所有微服务

```bash
# 使用开发环境配置启动所有服务
python3 services/user_vector_service/start_services.py --config configs/user_vector_service/development.yaml

# 使用生产环境配置启动所有服务
python3 services/user_vector_service/start_services.py --config configs/user_vector_service/production.yaml

# 不使用tmux启动服务
python3 services/user_vector_service/start_services.py --config configs/user_vector_service/development.yaml --no-tmux
```

### 2. 启动单个微服务

```bash
# 启动MongoDB读取服务（开发环境）
python3 -m services.user_vector_service.mongodb_reader_service.main \
  --config configs/user_vector_service/mongodb_reader_service/development.yaml \
  --host 0.0.0.0 --port 8003

# 启动向量处理服务（生产环境）
python3 -m services.user_vector_service.vector_processor_service.main \
  --config configs/user_vector_service/vector_processor_service/production.yaml \
  --host 0.0.0.0 --port 8004

# 启动向量写入服务
python3 -m services.user_vector_service.vector_writer_service.main \
  --config configs/user_vector_service/vector_writer_service/development.yaml \
  --host 0.0.0.0 --port 8005
```

### 3. 服务管理命令

```bash
# 检查服务状态
python3 services/user_vector_service/start_services.py --status

# 停止所有服务
python3 services/user_vector_service/start_services.py --stop
```

## 配置文件修改

### 修改主配置文件

如果需要更改微服务的配置文件路径，只需修改主配置文件中的 `services` 部分：

```yaml
services:
  mongodb_reader_service:
    config_file: "path/to/your/custom/config.yaml"
```

### 修改子服务配置

每个微服务的具体配置（如数据库连接、端口号等）都在各自的配置文件中定义，可以独立修改而不影响其他服务。

## 优势

1. **配置分离**: 每个微服务有独立的配置文件，便于管理和维护
2. **环境隔离**: 开发环境和生产环境配置完全分离
3. **灵活性**: 可以轻松更改单个服务的配置而不影响其他服务
4. **一致性**: 与 `orc_mongodb_service` 采用相同的配置管理方式
5. **可扩展性**: 添加新的微服务时只需在主配置文件中添加对应的配置文件路径

## 注意事项

1. 确保所有配置文件路径正确且文件存在
2. 修改配置后需要重启相应的服务才能生效
3. 生产环境配置文件中的敏感信息（如密码、token）应妥善保管
4. 建议在修改配置前备份原有配置文件
