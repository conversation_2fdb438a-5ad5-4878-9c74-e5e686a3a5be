#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
向量计算微服务实现

负责从Redis队列接收用户数据，查询Milvus内容向量，计算用户向量
"""

import os
import sys
import asyncio
import json
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from fastapi import FastAPI, HTTPException
import redis.asyncio as redis
import numpy as np

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from shared.core import ConfigManager, Logger, ExceptionHandler
from shared.database.milvus import MilvusPool, MilvusVectorOperations
from shared.utils import TimeUtils


@dataclass
class UserBatch:
    """用户批次数据"""
    task_id: str
    batch_id: str
    prov_id: int
    users: List[Dict[str, Any]]
    batch_index: int
    total_batches: int
    created_at: float


@dataclass
class VectorResult:
    """向量计算结果"""
    task_id: str
    batch_id: str
    prov_id: int
    user_vectors: List[Dict[str, Any]]  # [{"uid": int, "user_vector": List[float], "provid": int}]
    batch_index: int
    total_batches: int
    processed_at: float
    stats: Dict[str, Any]


class VectorProcessorService:
    """向量计算微服务"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.config = config_manager.get_config("vector_processor_service", default={})
        self.logger = Logger.get_logger(__name__)
        self.exception_handler = ExceptionHandler
        
        # 初始化组件
        self.milvus_pool = None
        self.milvus_ops = None
        self.redis_client = None
        self.pca_model = None
        
        # 服务状态
        self.is_running = False
        self.is_processing = False
        self.stats = {
            "total_batches_processed": 0,
            "total_users_processed": 0,
            "total_vectors_computed": 0,
            "failed_batches": 0,
            "start_time": time.time()
        }
        
        # 队列控制状态
        self.queue_paused = False
        self.queue_pause_start_time = None
        
        # 创建FastAPI应用
        self.app = FastAPI(title="Vector Processor Service", version="1.0.0")
        self._setup_routes()
    
    async def initialize(self):
        """初始化服务"""
        try:
            self.logger.info("初始化向量计算微服务...")
            
            # 初始化Milvus连接池
            self.milvus_pool = MilvusPool(config_manager=self.config_manager)
            await self.milvus_pool.initialize()
            self.milvus_ops = MilvusVectorOperations(self.milvus_pool, self.config_manager)
            
            # 初始化Redis连接
            redis_config = self.config.get("redis", {})
            self.redis_client = redis.Redis(
                host=redis_config.get("host", "localhost"),
                port=redis_config.get("port", 6379),
                db=redis_config.get("db", 0),
                decode_responses=True
            )
            
            # 测试Redis连接
            await self.redis_client.ping()
            
            # 加载PCA模型
            await self._load_pca_model()
            
            self.is_running = True
            self.logger.info("向量计算微服务初始化完成")
            
        except Exception as e:
            self.logger.error(f"服务初始化失败: {e}")
            raise
    
    async def _load_pca_model(self):
        """加载PCA模型"""
        try:
            import pickle
            
            pca_config = self.config.get("pca_config", {})
            model_path = pca_config.get("precomputed_pca_model_path", "models/pca_precomputed/latest_pca_model.pkl")
            
            if os.path.exists(model_path):
                with open(model_path, 'rb') as f:
                    self.pca_model = pickle.load(f)
                self.logger.info(f"PCA模型加载成功: {model_path}")
            else:
                self.logger.warning(f"PCA模型文件不存在: {model_path}")
                self.pca_model = None
                
        except Exception as e:
            self.logger.error(f"加载PCA模型失败: {e}")
            self.pca_model = None
    
    def _setup_routes(self):
        """设置API路由"""
        
        @self.app.get("/health")
        async def health_check():
            """健康检查"""
            if not self.is_running:
                raise HTTPException(status_code=503, detail="服务未就绪")
            
            return {
                "status": "healthy",
                "service": "vector_processor_service",
                "uptime": time.time() - self.stats["start_time"],
                "version": "1.0.0",
                "pca_model_loaded": self.pca_model is not None,
                "is_processing": self.is_processing
            }
        
        @self.app.get("/stats")
        async def get_stats():
            """获取服务统计信息"""
            return {
                "stats": self.stats,
                "is_processing": self.is_processing,
                "queue_status": {
                    "paused": self.queue_paused,
                    "pause_duration": time.time() - self.queue_pause_start_time if self.queue_pause_start_time else 0
                }
            }
        
        @self.app.get("/queue/status")
        async def get_queue_status():
            """获取队列状态"""
            try:
                redis_config = self.config.get("redis", {})
                input_queue = redis_config.get("vector_queue_name", "vector_processing_queue")
                output_queue = redis_config.get("storage_queue_name", "vector_storage_queue")
                
                input_length = await self.redis_client.llen(input_queue)
                output_length = await self.redis_client.llen(output_queue)
                
                return {
                    "input_queue": {
                        "name": input_queue,
                        "length": input_length
                    },
                    "output_queue": {
                        "name": output_queue,
                        "length": output_length
                    },
                    "is_processing": self.is_processing
                }
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"获取队列状态失败: {e}")
    
    async def start_queue_processing(self):
        """启动队列处理"""
        self.logger.info("启动队列处理...")
        asyncio.create_task(self._process_queue())
    
    async def _process_queue(self):
        """处理Redis队列中的任务"""
        redis_config = self.config.get("redis", {})
        input_queue_name = redis_config.get("vector_queue_name", "vector_processing_queue")
        
        self.logger.info(f"开始处理队列: {input_queue_name}")
        
        while self.is_running:
            try:
                # 从队列获取任务
                task_data = await self.redis_client.brpop(input_queue_name, timeout=1)
                
                if task_data:
                    self.is_processing = True
                    await self._process_user_batch(task_data[1])
                else:
                    self.is_processing = False
                    # 没有任务时短暂休眠
                    await asyncio.sleep(0.1)
                    
            except Exception as e:
                self.logger.error(f"队列处理错误: {e}")
                self.is_processing = False
                await asyncio.sleep(1)
    
    async def _process_user_batch(self, batch_json: str):
        """处理用户批次"""
        try:
            # 解析批次数据
            batch_data_dict = json.loads(batch_json)
            batch_data = UserBatch(**batch_data_dict)
            
            self.logger.info(f"开始处理用户批次: {batch_data.batch_id}, 用户数: {len(batch_data.users)}")
            
            # 获取用户向量
            user_vectors = await self._compute_user_vectors(batch_data.users)
            
            # 创建结果
            result = VectorResult(
                task_id=batch_data.task_id,
                batch_id=batch_data.batch_id,
                prov_id=batch_data.prov_id,
                user_vectors=user_vectors,
                batch_index=batch_data.batch_index,
                total_batches=batch_data.total_batches,
                processed_at=time.time(),
                stats={
                    "input_users": len(batch_data.users),
                    "output_vectors": len(user_vectors),
                    "processing_time": time.time() - batch_data.created_at
                }
            )
            
            # 检查输出队列状态
            await self._wait_for_output_queue_space()
            
            # 发送到存储队列
            await self._send_to_storage_queue(result)
            
            # 更新统计
            self.stats["total_batches_processed"] += 1
            self.stats["total_users_processed"] += len(batch_data.users)
            self.stats["total_vectors_computed"] += len(user_vectors)
            
            self.logger.info(f"用户批次处理完成: {batch_data.batch_id}, 计算向量数: {len(user_vectors)}")
            
        except Exception as e:
            self.stats["failed_batches"] += 1
            self.logger.error(f"处理用户批次失败: {e}")
            self.exception_handler.handle_exception(e)
    
    async def _compute_user_vectors(self, users: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """计算用户向量"""
        user_vectors = []
        
        try:
            # 收集所有PID
            all_pids = []
            user_pid_map = {}  # uid -> pids
            
            for user in users:
                uid = user["_id"]
                pid_groups = user.get("pid_groups", [])
                
                # 提取PID
                user_pids = []
                for group in pid_groups:
                    pids = group.get("pids", [])
                    user_pids.extend(pids)
                
                if user_pids:
                    user_pid_map[uid] = user_pids
                    all_pids.extend(user_pids)
            
            if not all_pids:
                self.logger.warning("没有找到有效的PID数据")
                return user_vectors
            
            # 去重PID
            unique_pids = list(set(all_pids))
            self.logger.debug(f"查询内容向量: 总PID数={len(all_pids)}, 去重后={len(unique_pids)}")
            
            # 批量查询内容向量
            content_vectors = await self._get_content_vectors(unique_pids)
            
            # 为每个用户计算向量
            for user in users:
                uid = user["_id"]
                prov_id = user.get("prov_id")
                
                if uid not in user_pid_map:
                    continue
                
                user_pids = user_pid_map[uid]
                
                # 获取用户的内容向量
                user_content_vectors = []
                for pid in user_pids:
                    if pid in content_vectors:
                        user_content_vectors.append(content_vectors[pid])
                
                # 计算用户向量
                if user_content_vectors:
                    user_vector = await self._aggregate_user_vector(user_content_vectors)
                    
                    if user_vector is not None:
                        user_vectors.append({
                            "uid": uid,
                            "user_vector": user_vector.tolist(),
                            "provid": prov_id
                        })
            
            return user_vectors
            
        except Exception as e:
            self.logger.error(f"计算用户向量失败: {e}")
            return []
    
    async def _get_content_vectors(self, pids: List[int]) -> Dict[int, np.ndarray]:
        """获取内容向量"""
        try:
            # 使用Milvus操作获取向量
            content_collection = self.config.get("milvus", {}).get("content_collection", "content_tower_collection_20250616")
            
            # 批量查询向量
            vectors_dict = await self.milvus_ops.get_vectors_by_ids(
                collection_name=content_collection,
                ids=pids,
                vector_field="item_embedding"
            )
            
            # 转换为numpy数组
            result = {}
            for pid, vector in vectors_dict.items():
                if vector is not None:
                    result[pid] = np.array(vector, dtype=np.float32)
            
            self.logger.debug(f"获取内容向量: 查询PID数={len(pids)}, 获得向量数={len(result)}")
            return result
            
        except Exception as e:
            self.logger.error(f"获取内容向量失败: {e}")
            return {}
    
    async def _aggregate_user_vector(self, content_vectors: List[np.ndarray]) -> Optional[np.ndarray]:
        """聚合用户向量"""
        try:
            if not content_vectors:
                return None
            
            # 检查向量配置
            vector_config = self.config.get("vector_config", {})
            min_pids_required = vector_config.get("min_pids_required", 3)
            max_pids_for_computation = vector_config.get("max_pids_for_computation", 50)
            aggregation_method = vector_config.get("aggregation_method", "weighted_mean")
            
            # 检查最少PID要求
            if len(content_vectors) < min_pids_required:
                return None
            
            # 限制最大PID数量
            if len(content_vectors) > max_pids_for_computation:
                content_vectors = content_vectors[:max_pids_for_computation]
            
            # 聚合向量
            if aggregation_method == "simple_mean":
                user_vector = np.mean(content_vectors, axis=0)
            elif aggregation_method == "weighted_mean":
                # 简单的权重策略：最近的向量权重更高
                weights = np.linspace(0.5, 1.0, len(content_vectors))
                weights = weights / np.sum(weights)
                user_vector = np.average(content_vectors, axis=0, weights=weights)
            else:
                # 默认使用简单平均
                user_vector = np.mean(content_vectors, axis=0)
            
            # PCA降维
            if self.pca_model is not None:
                user_vector = self.pca_model.transform(user_vector.reshape(1, -1))[0]
            
            return user_vector
            
        except Exception as e:
            self.logger.error(f"聚合用户向量失败: {e}")
            return None
    
    async def _check_output_queue_length(self) -> bool:
        """检查输出队列长度是否允许继续发送"""
        try:
            redis_config = self.config.get("redis", {})
            queue_control = redis_config.get("queue_control", {})
            
            storage_queue_name = redis_config.get("storage_queue_name", "vector_storage_queue")
            max_queue_size = queue_control.get("max_storage_queue_size", 50)
            
            queue_length = await self.redis_client.llen(storage_queue_name)
            
            if queue_length >= max_queue_size:
                if not self.queue_paused:
                    self.queue_paused = True
                    self.queue_pause_start_time = time.time()
                    self.logger.warning(f"存储队列长度超过限制 ({queue_length} >= {max_queue_size})，暂停发送")
                return False
            else:
                if self.queue_paused:
                    self.queue_paused = False
                    pause_duration = time.time() - self.queue_pause_start_time if self.queue_pause_start_time else 0
                    self.logger.info(f"存储队列长度恢复正常 ({queue_length} < {max_queue_size})，恢复发送，暂停时长: {pause_duration:.1f}秒")
                    self.queue_pause_start_time = None
                return True
                
        except Exception as e:
            self.logger.error(f"检查输出队列长度失败: {e}")
            return True  # 出错时允许继续发送
    
    async def _wait_for_output_queue_space(self):
        """等待输出队列有空间可用"""
        redis_config = self.config.get("redis", {})
        queue_control = redis_config.get("queue_control", {})
        check_interval = queue_control.get("check_interval", 5)

        while self.is_running:
            if await self._check_output_queue_length():
                break

            # 等待一段时间后再检查
            await asyncio.sleep(check_interval)
    
    async def _send_to_storage_queue(self, result: VectorResult):
        """发送结果到存储队列"""
        try:
            queue_name = self.config.get("redis", {}).get("storage_queue_name", "vector_storage_queue")
            
            # 序列化结果
            result_json = json.dumps(asdict(result), ensure_ascii=False)
            
            # 发送到Redis队列
            await self.redis_client.lpush(queue_name, result_json)
            
            self.logger.debug(f"向量结果已发送到存储队列: {queue_name}, 批次ID: {result.batch_id}")
            
        except Exception as e:
            self.logger.error(f"发送结果到存储队列失败: {e}")
            raise
    
    async def cleanup(self):
        """清理资源"""
        try:
            self.is_running = False
            
            if self.milvus_pool:
                await self.milvus_pool.close()
            
            if self.redis_client:
                await self.redis_client.close()
                
            self.logger.info("向量计算微服务资源清理完成")
            
        except Exception as e:
            self.logger.error(f"资源清理失败: {e}")
