#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MongoDB用户数据读取微服务实现

负责从MongoDB读取用户数据并发送到Redis队列
"""

import os
import sys
import asyncio
import json
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from fastapi import FastAPI, HTTPException, BackgroundTasks
from pydantic import BaseModel
import redis.asyncio as redis

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from shared.core import Con<PERSON>g<PERSON>ana<PERSON>, <PERSON><PERSON>, ExceptionHandler
from shared.database.mongodb import MongoDBPool, MongoDBOperations
from shared.utils import TimeUtils


class ReadTaskRequest(BaseModel):
    """读取任务请求"""
    prov_id: int
    batch_size: Optional[int] = None
    user_limit: Optional[int] = None


@dataclass
class ReadingTask:
    """读取任务"""
    task_id: str
    prov_id: int
    batch_size: int
    user_limit: Optional[int]
    status: str = "pending"
    created_at: float = 0.0
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    total_users: int = 0
    processed_users: int = 0
    error_message: Optional[str] = None


@dataclass
class UserBatch:
    """用户批次数据"""
    task_id: str
    batch_id: str
    prov_id: int
    users: List[Dict[str, Any]]
    batch_index: int
    total_batches: int
    created_at: float


class MongoDBReaderService:
    """MongoDB用户数据读取微服务"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.config = config_manager.get_config("mongodb_reader_service", default={})
        self.logger = Logger.get_logger(__name__)
        self.exception_handler = ExceptionHandler
        
        # 初始化组件
        self.mongodb_pool = None
        self.mongodb_ops = None
        self.redis_client = None
        
        # 服务状态
        self.is_running = False
        self.current_tasks = {}
        self.stats = {
            "total_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "total_users_read": 0,
            "total_batches_sent": 0,
            "start_time": time.time()
        }
        
        # 队列控制状态
        self.queue_paused = False
        self.queue_pause_start_time = None
        
        # 创建FastAPI应用
        self.app = FastAPI(title="MongoDB Reader Service", version="1.0.0")
        self._setup_routes()
    
    async def initialize(self):
        """初始化服务"""
        try:
            self.logger.info("初始化MongoDB用户数据读取微服务...")
            
            # 初始化MongoDB连接池
            self.mongodb_pool = MongoDBPool(config_manager=self.config_manager)
            await self.mongodb_pool.initialize()
            self.mongodb_ops = MongoDBOperations(self.mongodb_pool, self.config_manager)
            
            # 初始化Redis连接
            redis_config = self.config.get("redis", {})
            self.redis_client = redis.Redis(
                host=redis_config.get("host", "localhost"),
                port=redis_config.get("port", 6379),
                db=redis_config.get("db", 0),
                decode_responses=True
            )
            
            # 测试Redis连接
            await self.redis_client.ping()
            
            self.is_running = True
            self.logger.info("MongoDB用户数据读取微服务初始化完成")
            
        except Exception as e:
            self.logger.error(f"服务初始化失败: {e}")
            raise
    
    def _setup_routes(self):
        """设置API路由"""
        
        @self.app.get("/health")
        async def health_check():
            """健康检查"""
            if not self.is_running:
                raise HTTPException(status_code=503, detail="服务未就绪")
            
            return {
                "status": "healthy",
                "service": "mongodb_reader_service",
                "uptime": time.time() - self.stats["start_time"],
                "version": "1.0.0"
            }
        
        @self.app.get("/stats")
        async def get_stats():
            """获取服务统计信息"""
            return {
                "stats": self.stats,
                "current_tasks": len(self.current_tasks),
                "queue_status": {
                    "paused": self.queue_paused,
                    "pause_duration": time.time() - self.queue_pause_start_time if self.queue_pause_start_time else 0
                }
            }
        
        @self.app.post("/read")
        async def read_users(request: ReadTaskRequest, background_tasks: BackgroundTasks):
            """读取用户数据"""
            task_id = f"read_task_{int(time.time() * 1000)}"
            
            # 获取配置参数
            default_batch_size = self.config.get("batch_processing", {}).get("user_batch_size", 100000)
            batch_size = request.batch_size or default_batch_size
            
            task = ReadingTask(
                task_id=task_id,
                prov_id=request.prov_id,
                batch_size=batch_size,
                user_limit=request.user_limit,
                created_at=time.time()
            )
            
            self.current_tasks[task_id] = task
            background_tasks.add_task(self._read_users_async, task)
            
            return {
                "task_id": task_id,
                "status": "accepted",
                "message": "用户数据读取任务已接受"
            }
        
        @self.app.get("/task/{task_id}")
        async def get_task_status(task_id: str):
            """获取任务状态"""
            if task_id not in self.current_tasks:
                raise HTTPException(status_code=404, detail="任务不存在")
            
            task = self.current_tasks[task_id]
            return asdict(task)
    
    async def _read_users_async(self, task: ReadingTask):
        """异步读取用户数据"""
        try:
            self.logger.info(f"开始读取用户数据任务: {task.task_id}, 省份: {task.prov_id}")
            task.status = "processing"
            task.started_at = time.time()
            self.stats["total_tasks"] += 1
            
            # 构建集合名称（按省份筛选）
            collection_name = self._get_collection_name(task.prov_id)
            
            # 获取用户总数
            total_users = await self._get_total_user_count(collection_name, task.user_limit)
            task.total_users = total_users
            
            if total_users == 0:
                task.status = "completed"
                task.completed_at = time.time()
                self.stats["completed_tasks"] += 1
                self.logger.info(f"任务完成: {task.task_id}, 没有找到用户数据")
                return
            
            # 计算批次数
            total_batches = (total_users + task.batch_size - 1) // task.batch_size
            self.logger.info(f"开始读取用户数据: 总用户数={total_users}, 批次大小={task.batch_size}, 总批次数={total_batches}")
            
            # 分批读取用户数据
            batch_index = 0
            processed_users = 0
            
            async for user_batch in self._read_users_in_batches(collection_name, task.batch_size, task.user_limit):
                # 检查队列状态
                await self._wait_for_queue_space()
                
                # 创建用户批次
                batch_data = UserBatch(
                    task_id=task.task_id,
                    batch_id=f"{task.task_id}_batch_{batch_index}",
                    prov_id=task.prov_id,
                    users=user_batch,
                    batch_index=batch_index,
                    total_batches=total_batches,
                    created_at=time.time()
                )
                
                # 发送到队列
                await self._send_to_queue(batch_data)
                
                # 更新统计
                processed_users += len(user_batch)
                task.processed_users = processed_users
                self.stats["total_users_read"] += len(user_batch)
                self.stats["total_batches_sent"] += 1
                
                batch_index += 1
                
                self.logger.debug(f"已发送批次 {batch_index}/{total_batches}, 用户数: {len(user_batch)}")
            
            # 任务完成
            task.status = "completed"
            task.completed_at = time.time()
            self.stats["completed_tasks"] += 1
            
            self.logger.info(f"用户数据读取任务完成: {task.task_id}, 处理用户数: {processed_users}")
            
        except Exception as e:
            task.status = "failed"
            task.error_message = str(e)
            task.completed_at = time.time()
            self.stats["failed_tasks"] += 1
            
            self.logger.error(f"用户数据读取任务失败: {task.task_id}, 错误: {e}")
            self.exception_handler.handle_exception(e)
    
    def _get_collection_name(self, prov_id: int) -> str:
        """根据省份ID获取集合名称"""
        # 根据配置获取集合名称模式
        collection_pattern = self.config.get("mongodb", {}).get("collection_pattern", "user_pid_records_optimized_{prov_id}")
        return collection_pattern.format(prov_id=prov_id)
    
    async def _get_total_user_count(self, collection_name: str, user_limit: Optional[int]) -> int:
        """获取用户总数"""
        try:
            # 切换到指定集合
            self.mongodb_ops.set_collection(collection_name)
            
            # 构建查询条件（移除is_stored相关过滤）
            query_filter = {}
            
            # 获取总数
            total_count = await self.mongodb_ops.count_documents(query_filter)
            
            # 应用用户限制
            if user_limit and user_limit < total_count:
                return user_limit
            
            return total_count
            
        except Exception as e:
            self.logger.error(f"获取用户总数失败: {e}")
            return 0
    
    async def _read_users_in_batches(self, collection_name: str, batch_size: int, user_limit: Optional[int]):
        """分批读取用户数据"""
        try:
            # 切换到指定集合
            self.mongodb_ops.set_collection(collection_name)
            
            # 构建查询条件
            query_filter = {}
            
            # 投影字段
            projection = {
                "_id": 1,
                "pid_groups": 1,
                "prov_id": 1,
                "updated_days": 1
            }
            
            # 排序：按_id升序
            sort_criteria = [("_id", 1)]
            
            # 分批查询
            skip = 0
            total_read = 0
            
            while True:
                # 计算当前批次大小
                current_batch_size = batch_size
                if user_limit:
                    remaining = user_limit - total_read
                    if remaining <= 0:
                        break
                    current_batch_size = min(batch_size, remaining)
                
                # 查询当前批次
                users = await self.mongodb_ops.find_many(
                    query_filter,
                    projection=projection,
                    sort=sort_criteria,
                    skip=skip,
                    limit=current_batch_size
                )
                
                users_list = list(users)
                if not users_list:
                    break
                
                yield users_list
                
                skip += len(users_list)
                total_read += len(users_list)
                
                # 如果读取的用户数少于批次大小，说明已经读完
                if len(users_list) < current_batch_size:
                    break
                
        except Exception as e:
            self.logger.error(f"分批读取用户数据失败: {e}")
            raise
    
    async def _check_queue_length(self) -> bool:
        """检查队列长度是否允许继续发送"""
        try:
            redis_config = self.config.get("redis", {})
            queue_control = redis_config.get("queue_control", {})
            
            vector_queue_name = redis_config.get("vector_queue_name", "vector_processing_queue")
            max_queue_size = queue_control.get("max_queue_size", 100)
            
            queue_length = await self.redis_client.llen(vector_queue_name)
            
            if queue_length >= max_queue_size:
                if not self.queue_paused:
                    self.queue_paused = True
                    self.queue_pause_start_time = time.time()
                    self.logger.warning(f"队列长度超过限制 ({queue_length} >= {max_queue_size})，暂停发送")
                return False
            else:
                if self.queue_paused:
                    self.queue_paused = False
                    pause_duration = time.time() - self.queue_pause_start_time if self.queue_pause_start_time else 0
                    self.logger.info(f"队列长度恢复正常 ({queue_length} < {max_queue_size})，恢复发送，暂停时长: {pause_duration:.1f}秒")
                    self.queue_pause_start_time = None
                return True
                
        except Exception as e:
            self.logger.error(f"检查队列长度失败: {e}")
            return True  # 出错时允许继续发送
    
    async def _wait_for_queue_space(self):
        """等待队列有空间可用"""
        redis_config = self.config.get("redis", {})
        queue_control = redis_config.get("queue_control", {})
        check_interval = queue_control.get("check_interval", 5)

        while self.is_running:
            if await self._check_queue_length():
                break

            # 等待一段时间后再检查
            await asyncio.sleep(check_interval)
    
    async def _send_to_queue(self, batch_data: UserBatch):
        """发送用户批次到Redis队列"""
        try:
            queue_name = self.config.get("redis", {}).get("vector_queue_name", "vector_processing_queue")
            
            # 序列化批次数据
            batch_json = json.dumps(asdict(batch_data), ensure_ascii=False)
            
            # 发送到Redis队列
            await self.redis_client.lpush(queue_name, batch_json)
            
            self.logger.debug(f"用户批次已发送到队列: {queue_name}, 批次ID: {batch_data.batch_id}")
            
        except Exception as e:
            self.logger.error(f"发送用户批次到队列失败: {e}")
            raise
    
    async def cleanup(self):
        """清理资源"""
        try:
            self.is_running = False
            
            if self.mongodb_pool:
                await self.mongodb_pool.close()
            
            if self.redis_client:
                await self.redis_client.close()
                
            self.logger.info("MongoDB用户数据读取微服务资源清理完成")
            
        except Exception as e:
            self.logger.error(f"资源清理失败: {e}")
