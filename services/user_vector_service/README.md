# 用户向量服务

用户向量服务是 User-DF 系统的向量化处理服务，负责从 MongoDB 读取用户数据，通过 Milvus 获取内容向量，执行 PCA 降维计算用户向量，并将结果存储回 Milvus。该服务采用微服务架构，支持大规模向量计算和高并发处理。

## 🏗️ 服务架构

该服务包含三个微服务：

### MongoDB 读取微服务 (端口 8003)
- **功能**: 从 MongoDB 读取需要处理的用户数据
- **特性**:
  - 批量读取 (100,000 用户/批次)
  - 智能过滤 (vector_status.is_stored=false)
  - 乐观锁机制防止重复处理
  - 支持按 _id 范围查询

### 向量计算微服务 (端口 8004)
- **功能**: 执行向量计算和 PCA 降维
- **特性**:
  - 从 Milvus 批量获取内容向量 (512D)
  - PCA 降维处理 (512D → 256D)
  - 预计算 PCA 模型支持
  - 高效的向量运算优化

### 向量存储微服务 (端口 8005)
- **功能**: 将用户向量存储到 Milvus
- **特性**:
  - 批量向量存储
  - 自动索引管理
  - 存储状态更新
  - 错误恢复机制

## 🚀 快速开始

### 启动服务

```bash
# 启动所有微服务
python3 services/user_vector_service/start_services.py

# 使用指定配置启动
python3 services/user_vector_service/start_services.py --config configs/user_vector_service/production.yaml

# 检查服务状态
python3 services/user_vector_service/start_services.py --status

# 停止所有服务
python3 services/user_vector_service/start_services.py --stop
```

### 服务管理

```bash
# 查看所有 tmux 会话
tmux list-sessions

# 连接到 MongoDB 读取服务日志
tmux attach -t user-vector-reader

# 连接到向量计算服务日志
tmux attach -t user-vector-processor

# 连接到向量存储服务日志
tmux attach -t user-vector-writer
```

## ⚙️ 配置说明

### 主要配置文件

- `configs/user_vector_service/development.yaml` - 开发环境配置
- `configs/user_vector_service/production.yaml` - 生产环境配置

### 核心配置项

```yaml
# 用户处理配置
user_processing:
  batch_size: 100000                  # 用户批处理大小
  pid_batch_size: 15000               # PID 批处理大小
  max_pids_per_user: 300              # 每用户最大 PID 数

# MongoDB 配置
mongodb:
  connection_string: "mongodb://localhost:27017"
  database: "nrdc"
  collection: "user_pid_records_optimized"
  
# Milvus 配置
milvus:
  uri: "http://localhost:19530"
  database: "nrdc_db"
  content_collection: "content_tower_collection_20250616"
  user_collection: "user_tower_collection"

# PCA 配置
pca:
  model_path: "models/pca_precomputed/pca_model.pkl"
  input_dim: 512                      # 输入维度
  output_dim: 256                     # 输出维度
```

## 📊 数据处理流程

1. **用户数据读取**: 从 MongoDB 批量读取待处理用户
2. **PID 提取**: 提取用户的所有 PID 列表
3. **内容向量查询**: 从 Milvus 批量查询 PID 对应的内容向量
4. **向量聚合**: 计算用户的平均内容向量
5. **PCA 降维**: 使用预训练模型进行降维 (512D → 256D)
6. **向量存储**: 将用户向量存储到 Milvus 用户向量集合
7. **状态更新**: 更新 MongoDB 中的向量存储状态

## 🧮 向量计算

### PCA 降维模型

系统使用预训练的 PCA 模型进行降维：

```python
# PCA 模型训练 (离线)
from sklearn.decomposition import PCA
pca = PCA(n_components=256)
pca.fit(content_vectors_sample)  # 使用 100万 样本训练

# 在线降维
user_vector_256d = pca.transform(user_vector_512d)
```

### 向量聚合策略

- **平均聚合**: 对用户所有内容向量求平均
- **加权聚合**: 根据内容重要性加权 (可选)
- **归一化**: L2 归一化确保向量长度一致

## 🗄️ 数据模型

### Milvus 集合结构

#### 内容向量集合 (content_tower_collection_20250616)
```json
{
  "item_id": 1001,                    // 内容ID (INT64)
  "item_source": "news",              // 内容来源
  "item_embedding": [0.1, 0.2, ...], // 512D 向量
  "category_level4_id": 1001,         // 分类ID
  "timestamp": 1640995200             // 时间戳
}
```

#### 用户向量集合 (user_tower_collection)
```json
{
  "user_id": 123456789,               // 用户ID (INT64)
  "user_embedding": [0.1, 0.2, ...]  // 256D 用户向量
}
```

## 📈 性能特性

- **大规模处理**: 支持 20 亿用户的向量计算
- **批量优化**: 批量查询和存储提升性能
- **内存管理**: 智能的内存使用和释放
- **并发处理**: 多微服务并行计算
- **模型缓存**: PCA 模型内存缓存避免重复加载

## 🔍 监控和日志

### 日志文件
- `logs/UserVectorServiceManager_YYYYMMDD_HHMMSS.log` - 主服务日志
- 每个微服务在独立的 tmux 会话中运行

### 监控指标
- 用户处理速度 (用户/秒)
- 向量计算延迟
- Milvus 查询性能
- MongoDB 读写性能
- 内存和 CPU 使用率

## 🛠️ 开发和调试

### 本地开发

```bash
# 安装开发依赖
pip install -e .[dev]

# 运行单元测试
pytest tests/services/user_vector_service/

# 代码格式化
black services/user_vector_service/
isort services/user_vector_service/
```

### 调试模式

```bash
# 启动调试模式 (不使用 tmux)
python3 services/user_vector_service/start_services.py --no-tmux

# 查看详细日志
tail -f logs/UserVectorServiceManager_*.log
```

## 🚨 故障排除

### 常见问题

1. **Milvus 连接失败**
   - 检查 Milvus 服务状态
   - 验证连接配置 (uri, token, database)
   - 确认集合是否存在

2. **PCA 模型加载失败**
   - 检查模型文件路径
   - 验证模型文件完整性
   - 确认模型版本兼容性

3. **内存不足**
   - 调整批处理大小
   - 优化向量计算逻辑
   - 增加系统内存

4. **向量维度不匹配**
   - 检查 PCA 模型配置
   - 验证输入向量维度
   - 确认集合 schema 定义

### 性能优化建议

- 根据内存容量调整批处理大小
- 使用 GPU 加速向量计算 (可选)
- 优化 Milvus 索引参数
- 监控网络延迟和带宽
- 合理配置 PCA 模型缓存

## 📞 支持

如有问题或建议，请：
- 查看项目主 README 文档
- 提交 GitHub Issue
- 联系开发团队
