# User-DF 配置指南

本文档详细说明了 User-DF 系统的配置管理和各项配置参数。

## 📁 配置文件结构

```
configs/
├── orc_mongodb_service/           # ORC MongoDB 服务配置
│   ├── development.yaml           # 开发环境配置
│   ├── production.yaml            # 生产环境配置
│   └── testing.yaml               # 测试环境配置
└── user_vector_service/           # 用户向量服务配置
    ├── development.yaml           # 开发环境配置
    ├── production.yaml            # 生产环境配置
    └── testing.yaml               # 测试环境配置
```

## ⚙️ ORC MongoDB 服务配置

### 完整配置示例

```yaml
# ORC 文件处理配置
orc_processing:
  base_path: "orc_data"                     # ORC 文件基础路径
  file_pattern: "*.orc*"                    # 文件匹配模式
  batch_size: 1000                          # 批处理大小 (用户数/批次)
  chunk_size: 1000                          # 分块大小
  max_pids_per_user: 300                    # 每用户最大 PID 数
  
# MongoDB 数据库配置
mongodb:
  connection_string: "mongodb://localhost:27017"
  database: "nrdc"                          # 数据库名
  collection: "user_pid_records_optimized"  # 集合名
  batch_size: 1000                          # 批量写入大小
  cache_size_mb: 1024                       # 缓存大小 (MB)
  
# Milvus 向量数据库配置
milvus:
  uri: "http://localhost:19530"             # Milvus 服务地址
  token: ""                                 # 访问令牌 (可选)
  database: "nrdc_db"                       # 数据库名
  collection: "content_tower_collection_20250616"  # 内容向量集合
  batch_size: 15000                         # 批量查询大小
  timeout: 30                               # 查询超时 (秒)
  
# 性能优化配置
performance:
  batch_delay: 0.1                          # 批次间延迟 (秒)
  file_delay_multiplier: 0.001              # 文件处理延迟倍数
  max_memory_usage: "8GB"                   # 最大内存使用
  gc_threshold: 1000                        # 垃圾回收阈值
  
# 日志配置
logging:
  level: "INFO"                             # 日志级别
  file_path: "logs"                         # 日志文件路径
  max_file_size_mb: 100                     # 单文件最大大小 (MB)
  max_files: 10                             # 最大文件数量
  format: "human"                           # 日志格式 (human/json)
  
# 监控配置
monitoring:
  health_check_interval: 30                 # 健康检查间隔 (秒)
  metrics_collection: true                  # 是否收集指标
  performance_logging: true                 # 是否记录性能日志
```

### 关键配置说明

#### ORC 处理配置
- `base_path`: ORC 文件存储的基础路径
- `file_pattern`: 匹配 ORC 文件的模式，支持通配符
- `batch_size`: 每批次处理的用户数量，影响内存使用和处理速度
- `max_pids_per_user`: 限制每个用户的最大 PID 数量，防止数据倾斜

#### MongoDB 配置
- `connection_string`: MongoDB 连接字符串，支持副本集和分片
- `cache_size_mb`: MongoDB 客户端缓存大小，影响查询性能
- `batch_size`: 批量写入大小，平衡性能和内存使用

#### 性能优化配置
- `batch_delay`: 批次间延迟，用于控制系统负载
- `file_delay_multiplier`: 文件处理延迟倍数，根据处理记录数动态调整
- `max_memory_usage`: 最大内存使用限制，防止 OOM

## ⚙️ 用户向量服务配置

### 完整配置示例

```yaml
# 用户处理配置
user_processing:
  batch_size: 100000                        # 用户批处理大小
  pid_batch_size: 15000                     # PID 批处理大小
  max_pids_per_user: 300                    # 每用户最大 PID 数
  vector_dim: 256                           # 用户向量维度
  
# MongoDB 配置
mongodb:
  connection_string: "mongodb://localhost:27017"
  database: "nrdc"
  collection: "user_pid_records_optimized"
  query_timeout: 30                         # 查询超时 (秒)
  
# Milvus 配置
milvus:
  uri: "http://localhost:19530"
  token: ""
  database: "nrdc_db"
  content_collection: "content_tower_collection_20250616"
  user_collection: "user_tower_collection"
  batch_size: 15000
  timeout: 60
  
# PCA 降维配置
pca:
  model_path: "models/pca_precomputed/pca_model.pkl"
  input_dim: 512                            # 输入向量维度
  output_dim: 256                           # 输出向量维度
  cache_model: true                         # 是否缓存模型
  
# 向量计算配置
vector_computation:
  aggregation_method: "mean"                # 聚合方法 (mean/weighted)
  normalization: "l2"                       # 归一化方法 (l2/none)
  similarity_metric: "cosine"               # 相似度度量
  
# 性能配置
performance:
  max_memory_usage: "16GB"
  parallel_workers: 4                       # 并行工作进程数
  batch_timeout: 300                        # 批处理超时 (秒)
  
# 日志配置
logging:
  level: "INFO"
  file_path: "logs"
  max_file_size_mb: 100
  max_files: 10
  format: "human"
```

### 关键配置说明

#### 用户处理配置
- `batch_size`: 用户批处理大小，影响内存使用和查询性能
- `pid_batch_size`: PID 批处理大小，用于 Milvus 查询优化
- `vector_dim`: 用户向量维度，必须与 PCA 模型输出维度一致

#### PCA 配置
- `model_path`: 预训练 PCA 模型文件路径
- `input_dim/output_dim`: 输入和输出向量维度
- `cache_model`: 是否在内存中缓存模型，提升性能

#### 向量计算配置
- `aggregation_method`: 向量聚合方法，影响用户向量质量
- `normalization`: 向量归一化方法，确保向量长度一致
- `similarity_metric`: 相似度计算方法

## 🌍 环境配置

### 开发环境 (development.yaml)
```yaml
# 开发环境特定配置
mongodb:
  connection_string: "mongodb://localhost:27017"
  
milvus:
  uri: "http://localhost:19530"
  
logging:
  level: "DEBUG"                            # 详细日志
  
performance:
  batch_size: 100                           # 小批次便于调试
```

### 生产环境 (production.yaml)
```yaml
# 生产环境特定配置
mongodb:
  connection_string: "mongodb://prod-cluster:27017"
  cache_size_mb: 4096                       # 更大缓存
  
milvus:
  uri: "http://prod-milvus:19530"
  token: "${MILVUS_TOKEN}"                  # 环境变量
  
logging:
  level: "INFO"                             # 适中日志级别
  
performance:
  batch_size: 1000                          # 大批次提升性能
  max_memory_usage: "32GB"                  # 更多内存
```

### 测试环境 (testing.yaml)
```yaml
# 测试环境特定配置
mongodb:
  connection_string: "mongodb://test-db:27017"
  
milvus:
  uri: "http://test-milvus:19530"
  
logging:
  level: "WARNING"                          # 减少日志输出
  
performance:
  batch_size: 10                            # 小批次快速测试
```

## 🔧 配置管理

### 配置文件优先级
1. 命令行参数 (最高优先级)
2. 环境变量
3. 配置文件
4. 默认值 (最低优先级)

### 环境变量支持
```bash
# 数据库连接
export MONGODB_CONNECTION_STRING="mongodb://prod:27017"
export MILVUS_URI="http://prod-milvus:19530"
export MILVUS_TOKEN="your-token"

# 性能参数
export BATCH_SIZE=1000
export MAX_MEMORY_USAGE="16GB"

# 日志配置
export LOG_LEVEL="INFO"
export LOG_PATH="/var/log/user-df"
```

### 配置验证
系统启动时会自动验证配置：
- 必需参数检查
- 数据类型验证
- 取值范围检查
- 依赖关系验证

## 🔒 安全配置

### 敏感信息管理
```yaml
# 使用环境变量存储敏感信息
mongodb:
  connection_string: "${MONGODB_CONNECTION_STRING}"
  
milvus:
  token: "${MILVUS_TOKEN}"
  
# 或使用配置文件加密
security:
  encryption_key: "${ENCRYPTION_KEY}"
  encrypted_fields: ["mongodb.password", "milvus.token"]
```

### 访问控制
```yaml
security:
  enable_auth: true
  api_key: "${API_KEY}"
  allowed_ips: ["10.0.0.0/8", "***********/16"]
  rate_limit: 1000                          # 请求/分钟
```

## 📊 性能调优

### 内存优化
```yaml
performance:
  max_memory_usage: "16GB"                  # 根据系统内存调整
  gc_threshold: 1000                        # 垃圾回收阈值
  batch_size: 1000                          # 平衡内存和性能
```

### 并发优化
```yaml
performance:
  parallel_workers: 4                       # CPU 核心数
  connection_pool_size: 20                  # 数据库连接池
  max_concurrent_requests: 100              # 最大并发请求
```

### I/O 优化
```yaml
performance:
  batch_delay: 0.1                          # 控制 I/O 频率
  timeout_settings:
    mongodb_timeout: 30
    milvus_timeout: 60
    http_timeout: 10
```

## 🚨 故障排除

### 常见配置问题

1. **配置文件格式错误**
   - 检查 YAML 语法
   - 验证缩进和引号
   - 使用 YAML 验证工具

2. **环境变量未设置**
   - 检查环境变量是否存在
   - 验证变量名拼写
   - 确认变量值格式正确

3. **数据库连接失败**
   - 验证连接字符串格式
   - 检查网络连通性
   - 确认认证信息正确

4. **性能配置不当**
   - 根据系统资源调整批处理大小
   - 监控内存使用情况
   - 优化超时设置

### 配置验证工具
```bash
# 验证配置文件语法
python3 -m shared.core.config_manager --validate configs/orc_mongodb_service/development.yaml

# 测试数据库连接
python3 -m shared.database.mongodb --test-connection

# 检查系统资源
python3 -m shared.utils.monitoring --system-info
```
