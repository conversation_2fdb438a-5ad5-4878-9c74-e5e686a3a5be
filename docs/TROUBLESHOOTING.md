# User-DF 故障排除指南

本文档提供了 User-DF 系统常见问题的诊断和解决方案。

## 🔍 问题诊断流程

### 1. 快速检查清单

```bash
# 检查服务状态
python3 services/orc_mongodb_service/start_services.py --status
python3 services/user_vector_service/start_services.py --status

# 检查端口占用
netstat -tulpn | grep -E ":(8001|8002|8003|8004|8005)"

# 检查系统资源
free -h
df -h
top

# 检查日志
tail -f logs/*.log
```

### 2. 日志分析

```bash
# 查看最新错误日志
grep -i error logs/*.log | tail -20

# 查看特定时间段日志
grep "2024-07-29 15:" logs/*.log

# 统计错误类型
grep -i error logs/*.log | cut -d':' -f3 | sort | uniq -c
```

## 🚨 常见问题及解决方案

### 1. 服务启动问题

#### 问题：服务启动失败
```
错误信息: Failed to start service on port 8001
```

**诊断步骤：**
```bash
# 检查端口占用
lsof -i :8001

# 检查配置文件
python3 -c "import yaml; yaml.safe_load(open('configs/orc_mongodb_service/development.yaml'))"

# 检查依赖服务
systemctl status mongodb
docker ps | grep milvus
```

**解决方案：**
1. 终止占用端口的进程：`kill -9 <PID>`
2. 修改配置文件中的端口号
3. 启动依赖的数据库服务
4. 检查防火墙设置

#### 问题：Tmux 会话创建失败
```
错误信息: failed to create tmux session
```

**解决方案：**
```bash
# 安装 tmux
sudo apt install tmux  # Ubuntu/Debian
brew install tmux      # macOS

# 清理僵尸会话
tmux kill-server

# 手动创建会话测试
tmux new-session -d -s test-session
```

### 2. 数据库连接问题

#### 问题：MongoDB 连接失败
```
错误信息: pymongo.errors.ServerSelectionTimeoutError
```

**诊断步骤：**
```bash
# 测试 MongoDB 连接
mongo --host localhost --port 27017 --eval "db.runCommand('ping')"

# 检查 MongoDB 服务状态
systemctl status mongod

# 检查网络连通性
telnet mongodb-host 27017
```

**解决方案：**
1. 启动 MongoDB 服务：`systemctl start mongod`
2. 检查连接字符串格式
3. 验证认证信息
4. 检查网络防火墙设置

#### 问题：Milvus 连接失败
```
错误信息: MilvusException: <RpcError of RPC that terminated with: status = UNAVAILABLE>
```

**诊断步骤：**
```bash
# 检查 Milvus 服务状态
curl http://localhost:19530/health

# 检查 Docker 容器
docker ps | grep milvus
docker logs milvus-container

# 测试连接
python3 -c "from pymilvus import connections; connections.connect('default', host='localhost', port='19530')"
```

**解决方案：**
1. 重启 Milvus 服务：`docker restart milvus-container`
2. 检查 Milvus 配置文件
3. 验证网络连接
4. 检查磁盘空间

### 3. 性能问题

#### 问题：处理速度过慢
```
症状: 批处理时间过长，吞吐量低
```

**诊断步骤：**
```bash
# 监控系统资源
htop
iotop
nethogs

# 检查数据库性能
# MongoDB
db.user_pid_records_optimized.explain("executionStats").find({"vector_status.is_stored": false})

# 分析日志中的处理时间
grep "Processing time" logs/*.log | awk '{print $NF}' | sort -n
```

**解决方案：**
1. 调整批处理大小：
   ```yaml
   # 减小批处理大小
   orc_processing:
     batch_size: 500  # 从 1000 减少到 500
   ```

2. 优化数据库索引：
   ```javascript
   // MongoDB 索引优化
   db.user_pid_records_optimized.createIndex({"vector_status.is_stored": 1})
   db.user_pid_records_optimized.createIndex({"updated_days": 1})
   ```

3. 增加系统资源或调整配置：
   ```yaml
   performance:
     max_memory_usage: "16GB"  # 增加内存限制
     parallel_workers: 8       # 增加并行工作进程
   ```

#### 问题：内存使用过高
```
症状: 系统内存不足，出现 OOM 错误
```

**诊断步骤：**
```bash
# 监控内存使用
free -h
ps aux --sort=-%mem | head -10

# 检查 Python 进程内存
python3 -c "
import psutil
for p in psutil.process_iter(['pid', 'name', 'memory_info']):
    if 'python' in p.info['name']:
        print(f'{p.info[\"pid\"]}: {p.info[\"memory_info\"].rss / 1024 / 1024:.1f}MB')
"
```

**解决方案：**
1. 减小批处理大小
2. 增加垃圾回收频率：
   ```python
   import gc
   gc.collect()  # 在处理循环中定期调用
   ```
3. 优化数据结构，及时释放不需要的变量
4. 增加系统内存或使用内存更大的机器

### 4. 数据处理问题

#### 问题：ORC 文件读取失败
```
错误信息: pyarrow.lib.ArrowIOError: Failed to open local file
```

**诊断步骤：**
```bash
# 检查文件存在性和权限
ls -la orc_data/prov_id=100/statis_ymd=20240729/
file orc_data/prov_id=100/statis_ymd=20240729/*.orc

# 检查磁盘空间
df -h

# 测试文件读取
python3 -c "
import pyarrow.orc as orc
table = orc.read_table('path/to/file.orc')
print(table.schema)
"
```

**解决方案：**
1. 检查文件路径配置
2. 修复文件权限：`chmod 644 *.orc`
3. 验证文件完整性
4. 检查磁盘空间和 I/O 性能

#### 问题：向量计算错误
```
错误信息: ValueError: Input array has wrong dimensions
```

**诊断步骤：**
```bash
# 检查 PCA 模型
python3 -c "
import pickle
with open('models/pca_precomputed/pca_model.pkl', 'rb') as f:
    pca = pickle.load(f)
    print(f'Input dim: {pca.n_features_in_}')
    print(f'Output dim: {pca.n_components_}')
"

# 检查向量维度
python3 -c "
from pymilvus import Collection
collection = Collection('content_tower_collection_20250616')
print(collection.schema)
"
```

**解决方案：**
1. 验证 PCA 模型与配置一致
2. 检查输入向量维度
3. 重新训练或更新 PCA 模型
4. 验证 Milvus 集合 schema

### 5. 网络和连接问题

#### 问题：网络超时
```
错误信息: requests.exceptions.ReadTimeout
```

**诊断步骤：**
```bash
# 测试网络延迟
ping milvus-host
traceroute milvus-host

# 检查网络带宽
iperf3 -c milvus-host

# 监控网络连接
netstat -an | grep :19530
```

**解决方案：**
1. 增加超时设置：
   ```yaml
   milvus:
     timeout: 120  # 增加到 120 秒
   ```
2. 优化网络配置
3. 使用连接池
4. 实现重试机制

## 🛠️ 调试工具和技巧

### 1. 日志调试

```bash
# 启用详细日志
export LOG_LEVEL=DEBUG

# 实时监控日志
tail -f logs/*.log | grep -E "(ERROR|WARNING|Exception)"

# 日志分析脚本
python3 -c "
import re
import sys
from collections import Counter

errors = Counter()
with open('logs/orc_mongodb_service.log') as f:
    for line in f:
        if 'ERROR' in line:
            # 提取错误类型
            match = re.search(r'ERROR.*?(\w+Error|\w+Exception)', line)
            if match:
                errors[match.group(1)] += 1

for error, count in errors.most_common():
    print(f'{error}: {count}')
"
```

### 2. 性能分析

```bash
# 使用 cProfile 分析性能
python3 -m cProfile -o profile.stats services/orc_mongodb_service/main.py

# 分析结果
python3 -c "
import pstats
p = pstats.Stats('profile.stats')
p.sort_stats('cumulative').print_stats(20)
"

# 内存分析
pip install memory-profiler
python3 -m memory_profiler services/orc_mongodb_service/main.py
```

### 3. 数据库调试

```javascript
// MongoDB 查询分析
db.user_pid_records_optimized.find({"vector_status.is_stored": false}).explain("executionStats")

// 索引使用情况
db.user_pid_records_optimized.getIndexes()

// 集合统计信息
db.user_pid_records_optimized.stats()
```

```python
# Milvus 调试
from pymilvus import Collection, utility

# 检查集合信息
collection = Collection("content_tower_collection_20250616")
print(f"Entities: {collection.num_entities}")
print(f"Schema: {collection.schema}")

# 检查索引
print(f"Indexes: {collection.indexes}")

# 查询统计
utility.get_query_segment_info("content_tower_collection_20250616")
```

## 📊 监控和预警

### 1. 健康检查脚本

```bash
#!/bin/bash
# health_check.sh

# 检查服务状态
check_service() {
    local port=$1
    local service_name=$2
    
    if curl -s http://localhost:$port/health > /dev/null; then
        echo "✅ $service_name (port $port) is healthy"
        return 0
    else
        echo "❌ $service_name (port $port) is unhealthy"
        return 1
    fi
}

# 检查所有服务
check_service 8001 "ORC Processor"
check_service 8002 "MongoDB Writer"
check_service 8003 "MongoDB Reader"
check_service 8004 "Vector Processor"
check_service 8005 "Vector Writer"

# 检查数据库连接
if mongo --quiet --eval "db.runCommand('ping')" > /dev/null 2>&1; then
    echo "✅ MongoDB is accessible"
else
    echo "❌ MongoDB is not accessible"
fi

if curl -s http://localhost:19530/health > /dev/null; then
    echo "✅ Milvus is accessible"
else
    echo "❌ Milvus is not accessible"
fi
```

### 2. 自动恢复脚本

```bash
#!/bin/bash
# auto_recovery.sh

# 检查并重启失败的服务
restart_if_needed() {
    local service_script=$1
    
    if ! python3 $service_script --status > /dev/null 2>&1; then
        echo "Restarting $service_script"
        python3 $service_script --stop
        sleep 5
        python3 $service_script
    fi
}

# 监控和恢复
while true; do
    restart_if_needed "services/orc_mongodb_service/start_services.py"
    restart_if_needed "services/user_vector_service/start_services.py"
    sleep 60
done
```

## 📞 获取帮助

### 1. 收集诊断信息

```bash
#!/bin/bash
# collect_diagnostics.sh

echo "=== System Information ===" > diagnostics.txt
uname -a >> diagnostics.txt
free -h >> diagnostics.txt
df -h >> diagnostics.txt

echo "=== Service Status ===" >> diagnostics.txt
python3 services/orc_mongodb_service/start_services.py --status >> diagnostics.txt
python3 services/user_vector_service/start_services.py --status >> diagnostics.txt

echo "=== Recent Logs ===" >> diagnostics.txt
tail -100 logs/*.log >> diagnostics.txt

echo "=== Configuration ===" >> diagnostics.txt
cat configs/*/development.yaml >> diagnostics.txt

echo "Diagnostics collected in diagnostics.txt"
```

### 2. 联系支持

当遇到无法解决的问题时：

1. **收集诊断信息**：运行上述诊断脚本
2. **查看文档**：检查相关文档和 FAQ
3. **搜索已知问题**：在 GitHub Issues 中搜索类似问题
4. **提交问题报告**：
   - 详细描述问题现象
   - 提供错误日志和诊断信息
   - 说明环境配置和复现步骤
   - 附上配置文件（去除敏感信息）

### 3. 社区资源

- **GitHub Issues**: https://github.com/user-df/User-DF/issues
- **文档**: https://user-df.readthedocs.io/
- **邮件支持**: <EMAIL>
