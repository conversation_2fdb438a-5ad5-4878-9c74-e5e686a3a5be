# User-DF API 文档

本文档描述了 User-DF 系统各微服务提供的 API 接口。

## 🌐 API 概览

User-DF 系统包含 5 个微服务，每个服务提供 RESTful API 接口：

| 服务 | 端口 | 描述 |
|------|------|------|
| ORC 处理微服务 | 8001 | ORC 文件读取和数据解析 |
| MongoDB 写入微服务 | 8002 | 用户数据存储和管理 |
| MongoDB 读取微服务 | 8003 | 用户数据查询和过滤 |
| 向量计算微服务 | 8004 | 向量计算和 PCA 降维 |
| 向量存储微服务 | 8005 | 用户向量存储和状态更新 |

## 🔧 通用接口

所有微服务都提供以下通用接口：

### 健康检查

```http
GET /health
```

**响应示例：**
```json
{
  "status": "healthy",
  "service": "orc_processor",
  "timestamp": "2024-07-29T15:30:00Z",
  "version": "2.0.0",
  "uptime": 3600
}
```

### 服务信息

```http
GET /info
```

**响应示例：**
```json
{
  "service_name": "ORC处理微服务",
  "version": "2.0.0",
  "description": "处理 ORC 文件读取和数据解析",
  "port": 8001,
  "config": {
    "batch_size": 1000,
    "max_memory_usage": "8GB"
  }
}
```

### 指标监控

```http
GET /metrics
```

**响应示例：**
```json
{
  "processed_records": 150000,
  "processing_rate": 250.5,
  "memory_usage_mb": 2048,
  "cpu_usage_percent": 45.2,
  "error_count": 3,
  "last_error": "2024-07-29T15:25:00Z"
}
```

## 📁 ORC 处理微服务 (端口 8001)

### 处理 ORC 文件

```http
POST /process
```

**请求体：**
```json
{
  "prov_id": 100,
  "statis_ymd": "20240729",
  "batch_size": 1000,
  "file_pattern": "*.orc*"
}
```

**响应示例：**
```json
{
  "status": "success",
  "message": "ORC processing started",
  "task_id": "task_20240729_001",
  "estimated_records": 50000,
  "estimated_time_minutes": 20
}
```

### 查询处理状态

```http
GET /process/status/{task_id}
```

**响应示例：**
```json
{
  "task_id": "task_20240729_001",
  "status": "processing",
  "progress": {
    "processed_records": 25000,
    "total_records": 50000,
    "progress_percent": 50.0
  },
  "start_time": "2024-07-29T15:00:00Z",
  "estimated_completion": "2024-07-29T15:20:00Z"
}
```

### 获取处理结果

```http
GET /process/result/{task_id}
```

**响应示例：**
```json
{
  "task_id": "task_20240729_001",
  "status": "completed",
  "result": {
    "processed_records": 50000,
    "valid_records": 48500,
    "invalid_records": 1500,
    "processing_time_seconds": 1200,
    "output_batches": 50
  }
}
```

## 💾 MongoDB 写入微服务 (端口 8002)

### 批量写入用户数据

```http
POST /write/batch
```

**请求体：**
```json
{
  "users": [
    {
      "uid": 123456789,
      "pid_groups": [
        {
          "timestamp_days": 19000,
          "pids": [1001, 1002, 1003]
        }
      ],
      "prov_id": 100
    }
  ],
  "batch_options": {
    "upsert": true,
    "validate": true
  }
}
```

**响应示例：**
```json
{
  "status": "success",
  "result": {
    "inserted_count": 800,
    "updated_count": 150,
    "skipped_count": 50,
    "error_count": 0,
    "processing_time_ms": 2500
  }
}
```

### 查询写入统计

```http
GET /write/stats
```

**响应示例：**
```json
{
  "total_writes": 1500000,
  "successful_writes": 1485000,
  "failed_writes": 15000,
  "average_batch_size": 950,
  "average_write_time_ms": 2200,
  "last_write_time": "2024-07-29T15:30:00Z"
}
```

## 📖 MongoDB 读取微服务 (端口 8003)

### 批量查询用户数据

```http
POST /read/batch
```

**请求体：**
```json
{
  "query": {
    "vector_status.is_stored": false,
    "updated_days": {"$gte": 19000}
  },
  "batch_size": 100000,
  "skip": 0,
  "projection": {
    "uid": 1,
    "pid_groups": 1,
    "vector_status": 1
  }
}
```

**响应示例：**
```json
{
  "status": "success",
  "users": [
    {
      "uid": 123456789,
      "pid_groups": [
        {
          "timestamp_days": 19000,
          "pids": [1001, 1002, 1003]
        }
      ],
      "vector_status": {
        "is_stored": false,
        "stored_at_days": null
      }
    }
  ],
  "count": 95000,
  "has_more": true,
  "next_skip": 100000
}
```

### 查询用户统计

```http
GET /read/stats
```

**响应示例：**
```json
{
  "total_users": 2000000000,
  "users_with_vectors": 1500000000,
  "users_need_processing": 500000000,
  "average_pids_per_user": 245,
  "last_update_time": "2024-07-29T15:30:00Z"
}
```

## 🧮 向量计算微服务 (端口 8004)

### 计算用户向量

```http
POST /compute/user_vectors
```

**请求体：**
```json
{
  "users": [
    {
      "uid": 123456789,
      "pids": [1001, 1002, 1003, 1004, 1005]
    }
  ],
  "options": {
    "aggregation_method": "mean",
    "normalization": "l2",
    "pca_model_path": "models/pca_precomputed/pca_model.pkl"
  }
}
```

**响应示例：**
```json
{
  "status": "success",
  "results": [
    {
      "uid": 123456789,
      "user_vector": [0.1, 0.2, 0.3, ...],  // 256维向量
      "vector_norm": 1.0,
      "source_pids_count": 5,
      "valid_pids_count": 4
    }
  ],
  "processing_time_ms": 1500
}
```

### 批量查询内容向量

```http
POST /compute/content_vectors
```

**请求体：**
```json
{
  "pids": [1001, 1002, 1003, 1004, 1005],
  "batch_size": 15000
}
```

**响应示例：**
```json
{
  "status": "success",
  "vectors": {
    "1001": [0.1, 0.2, 0.3, ...],  // 512维向量
    "1002": [0.4, 0.5, 0.6, ...],
    "1003": [0.7, 0.8, 0.9, ...]
  },
  "found_count": 3,
  "missing_pids": [1004, 1005],
  "query_time_ms": 800
}
```

### PCA 模型信息

```http
GET /compute/pca_info
```

**响应示例：**
```json
{
  "model_path": "models/pca_precomputed/pca_model.pkl",
  "input_dimensions": 512,
  "output_dimensions": 256,
  "explained_variance_ratio": 0.85,
  "model_size_mb": 2.5,
  "last_updated": "2024-07-20T10:00:00Z"
}
```

## 💽 向量存储微服务 (端口 8005)

### 批量存储用户向量

```http
POST /store/user_vectors
```

**请求体：**
```json
{
  "vectors": [
    {
      "user_id": 123456789,
      "user_embedding": [0.1, 0.2, 0.3, ...]  // 256维向量
    }
  ],
  "options": {
    "update_mongodb_status": true,
    "create_index": false
  }
}
```

**响应示例：**
```json
{
  "status": "success",
  "result": {
    "stored_count": 95000,
    "failed_count": 0,
    "mongodb_updates": 95000,
    "storage_time_ms": 5000,
    "index_time_ms": 2000
  }
}
```

### 查询存储统计

```http
GET /store/stats
```

**响应示例：**
```json
{
  "total_stored_vectors": 1500000000,
  "storage_rate_per_second": 5000,
  "average_storage_time_ms": 3200,
  "milvus_collection_size": 1500000000,
  "last_storage_time": "2024-07-29T15:30:00Z"
}
```

### 更新 MongoDB 状态

```http
POST /store/update_status
```

**请求体：**
```json
{
  "user_ids": [123456789, 123456790, 123456791],
  "status_update": {
    "is_stored": true,
    "stored_at_days": 19000
  }
}
```

**响应示例：**
```json
{
  "status": "success",
  "updated_count": 3,
  "failed_count": 0,
  "update_time_ms": 150
}
```

## 🔒 认证和授权

### API 密钥认证

所有 API 请求需要在请求头中包含 API 密钥：

```http
Authorization: Bearer your-api-key-here
```

### 错误响应

当认证失败时，返回：

```json
{
  "error": "unauthorized",
  "message": "Invalid or missing API key",
  "code": 401
}
```

## 📊 错误处理

### 标准错误格式

```json
{
  "error": "error_type",
  "message": "Human readable error message",
  "code": 400,
  "details": {
    "field": "specific error details",
    "timestamp": "2024-07-29T15:30:00Z"
  }
}
```

### 常见错误码

| 错误码 | 描述 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 认证失败 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 429 | 请求频率限制 |
| 500 | 服务器内部错误 |
| 503 | 服务不可用 |

## 🚀 使用示例

### Python 客户端示例

```python
import requests
import json

# 配置
BASE_URL = "http://localhost"
API_KEY = "your-api-key"
HEADERS = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json"
}

# 健康检查
def check_health(port):
    response = requests.get(f"{BASE_URL}:{port}/health", headers=HEADERS)
    return response.json()

# 处理 ORC 文件
def process_orc_file(prov_id, statis_ymd):
    data = {
        "prov_id": prov_id,
        "statis_ymd": statis_ymd,
        "batch_size": 1000
    }
    response = requests.post(f"{BASE_URL}:8001/process", 
                           headers=HEADERS, json=data)
    return response.json()

# 查询用户数据
def query_users(batch_size=100000):
    data = {
        "query": {"vector_status.is_stored": False},
        "batch_size": batch_size
    }
    response = requests.post(f"{BASE_URL}:8003/read/batch", 
                           headers=HEADERS, json=data)
    return response.json()

# 使用示例
if __name__ == "__main__":
    # 检查所有服务健康状态
    for port in [8001, 8002, 8003, 8004, 8005]:
        health = check_health(port)
        print(f"Service on port {port}: {health['status']}")
    
    # 处理 ORC 文件
    result = process_orc_file(100, "20240729")
    print(f"Processing task: {result['task_id']}")
    
    # 查询待处理用户
    users = query_users(1000)
    print(f"Found {users['count']} users to process")
```

### cURL 示例

```bash
# 健康检查
curl -H "Authorization: Bearer your-api-key" \
     http://localhost:8001/health

# 处理 ORC 文件
curl -X POST \
     -H "Authorization: Bearer your-api-key" \
     -H "Content-Type: application/json" \
     -d '{"prov_id": 100, "statis_ymd": "20240729", "batch_size": 1000}' \
     http://localhost:8001/process

# 查询用户数据
curl -X POST \
     -H "Authorization: Bearer your-api-key" \
     -H "Content-Type: application/json" \
     -d '{"query": {"vector_status.is_stored": false}, "batch_size": 1000}' \
     http://localhost:8003/read/batch
```

## 📈 性能和限制

### 请求限制

- **请求频率**: 1000 请求/分钟/IP
- **批处理大小**: 最大 100,000 记录/请求
- **请求超时**: 60 秒
- **响应大小**: 最大 100MB

### 性能指标

- **ORC 处理**: ~250 用户/秒
- **MongoDB 写入**: ~400 用户/秒
- **MongoDB 读取**: ~50,000 用户/秒
- **向量计算**: ~100 用户/秒
- **向量存储**: ~200 用户/秒

### 最佳实践

1. **批量操作**: 尽量使用批量接口提升性能
2. **分页查询**: 大量数据查询使用分页避免超时
3. **错误重试**: 实现指数退避重试机制
4. **连接复用**: 使用连接池减少连接开销
5. **监控指标**: 定期检查 `/metrics` 接口监控性能
