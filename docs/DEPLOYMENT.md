# User-DF 部署指南

本文档提供了 User-DF 系统在不同环境下的部署指南和最佳实践。

## 🏗️ 系统要求

### 硬件要求

#### 最小配置
- **CPU**: 4 核心
- **内存**: 8GB RAM
- **存储**: 100GB SSD
- **网络**: 1Gbps

#### 推荐配置
- **CPU**: 16 核心
- **内存**: 64GB RAM
- **存储**: 1TB NVMe SSD
- **网络**: 10Gbps

#### 生产环境配置
- **CPU**: 32+ 核心
- **内存**: 128GB+ RAM
- **存储**: 2TB+ NVMe SSD
- **网络**: 10Gbps+

### 软件要求

- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / macOS 10.15+
- **Python**: 3.8+
- **MongoDB**: 4.4+
- **Milvus**: 2.5+
- **Redis**: 6.0+
- **Tmux**: 3.0+

## 🐳 Docker 部署

### 1. 使用 Docker Compose

创建 `docker-compose.yml`:

```yaml
version: '3.8'

services:
  # MongoDB 服务
  mongodb:
    image: mongo:4.4
    container_name: user-df-mongodb
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
    
  # Milvus 服务
  milvus:
    image: milvusdb/milvus:v2.5.0
    container_name: user-df-milvus
    ports:
      - "19530:19530"
    volumes:
      - milvus_data:/var/lib/milvus
    environment:
      ETCD_ENDPOINTS: etcd:2379
      MINIO_ADDRESS: minio:9000
    depends_on:
      - etcd
      - minio
      
  # Redis 服务
  redis:
    image: redis:6.2
    container_name: user-df-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      
  # User-DF 应用
  user-df:
    build: .
    container_name: user-df-app
    ports:
      - "8001-8005:8001-8005"
    volumes:
      - ./configs:/app/configs
      - ./logs:/app/logs
      - ./models:/app/models
    depends_on:
      - mongodb
      - milvus
      - redis
    environment:
      MONGODB_CONNECTION_STRING: **************************************
      MILVUS_URI: http://milvus:19530
      REDIS_URL: redis://redis:6379

volumes:
  mongodb_data:
  milvus_data:
  redis_data:
```

### 2. 构建和启动

```bash
# 构建镜像
docker-compose build

# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f user-df
```

## 🖥️ 本地部署

### 1. 环境准备

```bash
# 安装系统依赖
sudo apt update
sudo apt install -y python3.8 python3-pip tmux

# 安装 MongoDB
wget -qO - https://www.mongodb.org/static/pgp/server-4.4.asc | sudo apt-key add -
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/4.4 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-4.4.list
sudo apt update
sudo apt install -y mongodb-org

# 安装 Milvus (使用 Docker)
docker run -d --name milvus -p 19530:19530 milvusdb/milvus:v2.5.0

# 安装 Redis
sudo apt install -y redis-server
```

### 2. 项目部署

```bash
# 克隆项目
git clone https://github.com/user-df/User-DF.git
cd User-DF

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -e .[all]

# 配置文件
cp configs/orc_mongodb_service/development.yaml.template configs/orc_mongodb_service/development.yaml
cp configs/user_vector_service/development.yaml.template configs/user_vector_service/development.yaml

# 编辑配置文件
vim configs/orc_mongodb_service/development.yaml
vim configs/user_vector_service/development.yaml
```

### 3. 启动服务

```bash
# 启动 ORC MongoDB 服务
python3 services/orc_mongodb_service/start_services.py

# 启动用户向量服务
python3 services/user_vector_service/start_services.py

# 检查服务状态
python3 services/orc_mongodb_service/start_services.py --status
python3 services/user_vector_service/start_services.py --status
```

## ☁️ 云端部署

### 1. AWS 部署

#### 使用 ECS (Elastic Container Service)

```yaml
# ecs-task-definition.json
{
  "family": "user-df-task",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "2048",
  "memory": "8192",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "user-df",
      "image": "your-account.dkr.ecr.region.amazonaws.com/user-df:latest",
      "portMappings": [
        {"containerPort": 8001, "protocol": "tcp"},
        {"containerPort": 8002, "protocol": "tcp"},
        {"containerPort": 8003, "protocol": "tcp"},
        {"containerPort": 8004, "protocol": "tcp"},
        {"containerPort": 8005, "protocol": "tcp"}
      ],
      "environment": [
        {"name": "MONGODB_CONNECTION_STRING", "value": "mongodb://documentdb-cluster:27017"},
        {"name": "MILVUS_URI", "value": "http://milvus-service:19530"}
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/user-df",
          "awslogs-region": "us-west-2",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

#### 部署脚本

```bash
#!/bin/bash
# deploy-aws.sh

# 构建和推送镜像
docker build -t user-df .
docker tag user-df:latest $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/user-df:latest
docker push $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/user-df:latest

# 更新 ECS 服务
aws ecs register-task-definition --cli-input-json file://ecs-task-definition.json
aws ecs update-service --cluster user-df-cluster --service user-df-service --task-definition user-df-task
```

### 2. Kubernetes 部署

#### Deployment 配置

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-df-deployment
  labels:
    app: user-df
spec:
  replicas: 3
  selector:
    matchLabels:
      app: user-df
  template:
    metadata:
      labels:
        app: user-df
    spec:
      containers:
      - name: user-df
        image: user-df:latest
        ports:
        - containerPort: 8001
        - containerPort: 8002
        - containerPort: 8003
        - containerPort: 8004
        - containerPort: 8005
        env:
        - name: MONGODB_CONNECTION_STRING
          valueFrom:
            secretKeyRef:
              name: user-df-secrets
              key: mongodb-connection
        - name: MILVUS_URI
          value: "http://milvus-service:19530"
        resources:
          requests:
            memory: "4Gi"
            cpu: "1000m"
          limits:
            memory: "8Gi"
            cpu: "2000m"
        volumeMounts:
        - name: config-volume
          mountPath: /app/configs
        - name: logs-volume
          mountPath: /app/logs
      volumes:
      - name: config-volume
        configMap:
          name: user-df-config
      - name: logs-volume
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: user-df-service
spec:
  selector:
    app: user-df
  ports:
  - name: orc-processor
    port: 8001
    targetPort: 8001
  - name: mongodb-writer
    port: 8002
    targetPort: 8002
  - name: mongodb-reader
    port: 8003
    targetPort: 8003
  - name: vector-processor
    port: 8004
    targetPort: 8004
  - name: vector-writer
    port: 8005
    targetPort: 8005
  type: LoadBalancer
```

#### 部署命令

```bash
# 创建命名空间
kubectl create namespace user-df

# 创建配置和密钥
kubectl create configmap user-df-config --from-file=configs/ -n user-df
kubectl create secret generic user-df-secrets --from-literal=mongodb-connection="mongodb://..." -n user-df

# 部署应用
kubectl apply -f k8s-deployment.yaml -n user-df

# 查看状态
kubectl get pods -n user-df
kubectl get services -n user-df
```

## 🔧 生产环境配置

### 1. 数据库优化

#### MongoDB 配置

```yaml
# /etc/mongod.conf
storage:
  dbPath: /var/lib/mongodb
  journal:
    enabled: true
  wiredTiger:
    engineConfig:
      cacheSizeGB: 32
      
systemLog:
  destination: file
  logAppend: true
  path: /var/log/mongodb/mongod.log
  
net:
  port: 27017
  bindIp: 0.0.0.0
  
replication:
  replSetName: "rs0"
  
sharding:
  clusterRole: shardsvr
```

#### Milvus 配置

```yaml
# milvus.yaml
etcd:
  endpoints:
    - etcd:2379
    
minio:
  address: minio:9000
  accessKeyID: minioadmin
  secretAccessKey: minioadmin
  
common:
  defaultPartitionName: "_default"
  defaultIndexName: "_default_idx"
  
dataCoord:
  segment:
    maxSize: 512
    
queryCoord:
  autoHandoff: true
  autoBalance: true
```

### 2. 系统优化

#### 内核参数调优

```bash
# /etc/sysctl.conf
vm.max_map_count=262144
vm.swappiness=1
net.core.somaxconn=65535
net.ipv4.tcp_max_syn_backlog=65535
fs.file-max=1000000
```

#### 服务配置

```bash
# /etc/systemd/system/user-df.service
[Unit]
Description=User-DF Service
After=network.target mongodb.service

[Service]
Type=forking
User=user-df
Group=user-df
WorkingDirectory=/opt/user-df
ExecStart=/opt/user-df/scripts/start-production.sh
ExecStop=/opt/user-df/scripts/stop-production.sh
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

## 📊 监控和日志

### 1. 监控配置

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'user-df'
    static_configs:
      - targets: ['localhost:8001', 'localhost:8002', 'localhost:8003', 'localhost:8004', 'localhost:8005']
    metrics_path: /metrics
    scrape_interval: 5s
```

### 2. 日志聚合

```yaml
# filebeat.yml
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /opt/user-df/logs/*.log
  fields:
    service: user-df
    environment: production

output.elasticsearch:
  hosts: ["elasticsearch:9200"]
  index: "user-df-logs-%{+yyyy.MM.dd}"
```

## 🚨 故障排除

### 常见部署问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :8001
   
   # 修改配置文件中的端口
   vim configs/orc_mongodb_service/production.yaml
   ```

2. **内存不足**
   ```bash
   # 监控内存使用
   free -h
   
   # 调整批处理大小
   vim configs/*/production.yaml
   ```

3. **数据库连接失败**
   ```bash
   # 测试 MongoDB 连接
   mongo --host mongodb-host --port 27017
   
   # 测试 Milvus 连接
   curl http://milvus-host:19530/health
   ```

### 性能调优

1. **批处理大小优化**
   - 根据内存大小调整 batch_size
   - 监控处理速度和内存使用
   - 平衡吞吐量和延迟

2. **数据库索引优化**
   ```javascript
   // MongoDB 索引
   db.user_pid_records_optimized.createIndex({"_id": 1})
   db.user_pid_records_optimized.createIndex({"vector_status.is_stored": 1})
   ```

3. **系统资源监控**
   ```bash
   # CPU 使用率
   top -p $(pgrep -f user-df)
   
   # 内存使用
   ps aux | grep user-df
   
   # 磁盘 I/O
   iotop -p $(pgrep -f user-df)
   ```

## 🔒 安全配置

### 1. 网络安全

```bash
# 防火墙配置
ufw allow from 10.0.0.0/8 to any port 8001:8005
ufw deny 8001:8005

# SSL/TLS 配置
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365
```

### 2. 访问控制

```yaml
# nginx.conf
upstream user-df {
    server 127.0.0.1:8001;
    server 127.0.0.1:8002;
    server 127.0.0.1:8003;
    server 127.0.0.1:8004;
    server 127.0.0.1:8005;
}

server {
    listen 443 ssl;
    server_name user-df.example.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://user-df;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```
