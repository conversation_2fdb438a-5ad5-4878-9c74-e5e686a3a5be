# User-DF 系统架构设计

## 🏛️ 总体架构

User-DF 采用微服务架构设计，将复杂的用户数据处理和向量化任务分解为多个独立的微服务，每个服务专注于特定的功能领域。

```
┌─────────────────────────────────────────────────────────────┐
│                    User-DF 系统架构                          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────────────────────┐  │
│  │  ORC MongoDB    │    │     User Vector Service        │  │
│  │    Service      │    │                                 │  │
│  │                 │    │                                 │  │
│  │ ┌─────────────┐ │    │ ┌─────────────┐ ┌─────────────┐ │  │
│  │ │ORC Processor│ │    │ │MongoDB      │ │Vector       │ │  │
│  │ │   :8001     │ │    │ │Reader :8003 │ │Processor    │ │  │
│  │ └─────────────┘ │    │ └─────────────┘ │   :8004     │ │  │
│  │ ┌─────────────┐ │    │ ┌─────────────┐ └─────────────┘ │  │
│  │ │MongoDB      │ │    │ │Vector       │ ┌─────────────┐ │  │
│  │ │Writer :8002 │ │    │ │Writer :8005 │ │Monitoring   │ │  │
│  │ └─────────────┘ │    │ └─────────────┘ │   Service   │ │  │
│  └─────────────────┘    └─────────────────┘ └─────────────┘ │  │
├─────────────────────────────────────────────────────────────┤
│                      Shared Modules                        │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌───────┐ │
│  │  Core   │ │Database │ │ Models  │ │  Queue  │ │ Utils │ │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘ └───────┘ │
├─────────────────────────────────────────────────────────────┤
│                     Data Storage Layer                     │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐           │
│  │MongoDB  │ │ Milvus  │ │ Redis   │ │  Hive   │           │
│  │(用户数据)│ │(向量数据)│ │(缓存)   │ │(ORC文件)│           │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘           │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 微服务设计

### 1. ORC MongoDB 服务

#### 1.1 ORC 处理微服务 (端口 8001)
- **职责**: ORC 文件读取和数据解析
- **输入**: ORC 分区数据 (prov_id/statis_ymd)
- **输出**: 结构化用户数据
- **特性**:
  - 支持分区数据处理
  - 批量数据读取 (1000 用户/批次)
  - 内存优化处理
  - 错误恢复机制

#### 1.2 MongoDB 写入微服务 (端口 8002)
- **职责**: 用户数据存储和管理
- **输入**: 处理后的用户数据
- **输出**: MongoDB 存储确认
- **特性**:
  - 批量写入优化
  - 数据去重和验证
  - 索引自动管理
  - 乐观锁机制

### 2. 用户向量服务

#### 2.1 MongoDB 读取微服务 (端口 8003)
- **职责**: 用户数据查询和过滤
- **输入**: 查询条件和批次参数
- **输出**: 待处理用户列表
- **特性**:
  - 批量查询 (100,000 用户/批次)
  - 智能过滤条件
  - 分页查询支持
  - 状态跟踪

#### 2.2 向量计算微服务 (端口 8004)
- **职责**: 向量计算和 PCA 降维
- **输入**: 用户 PID 列表
- **输出**: 256D 用户向量
- **特性**:
  - 批量向量查询
  - PCA 降维处理
  - 向量聚合算法
  - 模型缓存机制

#### 2.3 向量存储微服务 (端口 8005)
- **职责**: 用户向量存储和状态更新
- **输入**: 用户向量数据
- **输出**: 存储确认和状态更新
- **特性**:
  - 批量向量存储
  - 自动索引管理
  - 状态同步更新
  - 错误重试机制

## 🗄️ 数据架构

### 数据流向

```
ORC Files → ORC Processor → MongoDB Writer → MongoDB
                                ↓
MongoDB ← Vector Writer ← Vector Processor ← MongoDB Reader
    ↓                           ↓
Status Update              Milvus (Content Vectors)
                                ↓
                          Milvus (User Vectors)
```

### 数据模型设计

#### MongoDB 用户文档
```json
{
  "_id": 123456789,                    // 用户ID (作为主键)
  "uid": 123456789,                    // 用户ID
  "pid_groups": [                      // PID 分组 (按时间戳)
    {
      "timestamp_days": 19000,         // 时间戳 (天数)
      "pids": [1001, 1002, 1003]       // PID 列表
    }
  ],
  "pid_count": 300,                    // PID 总数 (限制300个)
  "created_days": 19000,               // 创建时间
  "updated_days": 19000,               // 更新时间
  "vector_status": {                   // 向量状态
    "is_stored": false,                // 是否已存储向量
    "stored_at_days": null             // 存储时间
  },
  "prov_id": 100                       // 省份ID
}
```

#### Milvus 向量集合

**内容向量集合**:
- `item_id` (INT64): 内容ID
- `item_embedding` (FLOAT_VECTOR[512]): 内容向量
- `item_source` (VARCHAR): 内容来源
- `category_level4_id` (INT64): 分类ID
- `timestamp` (INT64): 时间戳

**用户向量集合**:
- `user_id` (INT64): 用户ID
- `user_embedding` (FLOAT_VECTOR[256]): 用户向量

## 🔄 处理流程

### 1. ORC 数据处理流程

```
1. 扫描 ORC 文件 (orc_data/prov_id=X/statis_ymd=YYYYMMDD)
2. 分批读取用户数据 (1000 用户/批次)
3. 数据清洗和格式化
4. PID 去重和验证
5. Milvus PID 存在性检查
6. 构建 MongoDB 文档结构
7. 批量写入 MongoDB
8. 更新处理状态
```

### 2. 用户向量化流程

```
1. 查询待处理用户 (vector_status.is_stored=false)
2. 批量读取用户数据 (100,000 用户/批次)
3. 提取所有用户的 PID 列表
4. 批量查询 Milvus 内容向量 (15,000 PID/批次)
5. 计算用户平均向量 (512D)
6. PCA 降维处理 (512D → 256D)
7. 批量存储用户向量到 Milvus
8. 更新 MongoDB 向量状态
```

## 🏗️ 技术架构

### 编程语言和框架
- **Python 3.8+**: 主要开发语言
- **PyMongo**: MongoDB 客户端
- **PyMilvus**: Milvus 向量数据库客户端
- **Scikit-learn**: PCA 降维和机器学习
- **PyArrow**: ORC 文件读取
- **Pandas/Numpy**: 数据处理

### 数据存储
- **MongoDB**: 用户数据存储，支持分片和索引
- **Milvus**: 向量数据库，支持高维向量检索
- **Redis**: 缓存和会话存储
- **ORC 文件**: 本地文件系统存储

### 服务治理
- **Tmux**: 进程管理和会话隔离
- **YAML**: 配置文件管理
- **Logging**: 结构化日志记录
- **Health Check**: 服务健康检查

## 🔧 配置管理

### 配置文件结构
```
configs/
├── orc_mongodb_service/
│   ├── development.yaml
│   ├── production.yaml
│   └── testing.yaml
└── user_vector_service/
    ├── development.yaml
    ├── production.yaml
    └── testing.yaml
```

### 配置层次
1. **默认配置**: 代码中的默认值
2. **环境配置**: YAML 配置文件
3. **命令行参数**: 运行时参数覆盖
4. **环境变量**: 敏感信息配置

## 📊 性能设计

### 批处理优化
- **ORC 处理**: 1000 用户/批次
- **MongoDB 读取**: 100,000 用户/批次
- **Milvus 查询**: 15,000 PID/批次
- **向量存储**: 批量插入优化

### 内存管理
- 分块处理避免内存溢出
- 及时释放不需要的数据
- 模型缓存减少重复加载
- 垃圾回收优化

### 并发处理
- 微服务并行执行
- 异步 I/O 操作
- 连接池管理
- 资源隔离

## 🔒 安全设计

### 数据安全
- 数据库连接加密
- 敏感信息配置保护
- 访问权限控制
- 数据传输安全

### 服务安全
- 服务间认证
- API 访问控制
- 日志脱敏处理
- 错误信息保护

## 🚀 扩展性设计

### 水平扩展
- 微服务独立扩展
- 数据库分片支持
- 负载均衡机制
- 服务发现机制

### 垂直扩展
- 资源配置优化
- 性能参数调优
- 硬件资源升级
- 算法优化改进

## 📈 监控和运维

### 监控指标
- 服务健康状态
- 处理性能指标
- 资源使用情况
- 错误率统计

### 日志管理
- 结构化日志格式
- 日志级别管理
- 日志轮转机制
- 日志聚合分析

### 运维工具
- 服务启停脚本
- 健康检查工具
- 性能监控面板
- 故障诊断工具
